# GUI QR码和更新界面实现文档

## 概述
本文档描述了为GUI系统添加的两个新界面：QR码界面（GUI_QR_CODE_SCR_ID）和更新界面（GUI_UPDATING_SCR_ID）的实现。

## 实现的功能

### 1. QR码界面 (GUI_QR_CODE_SCR_ID)

#### 功能特性：
- 显示WiFi连接的QR码
- 支持多语言（英语/西班牙语）
- 自动获取WiFi SSID和密码
- 生成标准WiFi QR码格式

#### 实现的函数：
- `qr_code_scr_create()` - 创建QR码界面
- `qr_code_scr_enter()` - 进入QR码界面
- `qr_code_scr_exit()` - 退出QR码界面

#### 界面元素：
- 标题标签（"QR Code" / "Código QR"）
- QR码显示区域（150x150像素）
- 说明文字（"Scan to connect WiFi" / "Escanee para conectar WiFi"）

#### QR码数据格式：
```
WIFI:T:WPA;S:<SSID>;P:<PASSWORD>;H:false;;  // 有密码的WiFi
WIFI:T:nopass;S:<SSID>;;;                   // 无密码的WiFi
```

### 2. 更新界面 (GUI_UPDATING_SCR_ID)

#### 功能特性：
- 显示系统更新进度
- 支持多语言显示
- 动态进度条指示
- 定时器刷新机制

#### 实现的函数：
- `updating_scr_create()` - 创建更新界面
- `updating_scr_enter()` - 进入更新界面
- `updating_scr_exit()` - 退出更新界面
- `updating_timer_handler()` - 定时器处理函数

#### 界面元素：
- 标题标签（"Updating..." / "Actualizando..."）
- 进度条（200x20像素）
- 进度图标（复用现有的GUI_PROGRESS_ICON）

## 代码修改详情

### 1. 文件修改列表
- `trc-gui/src/win_manager.c` - 主要实现文件
- `trc-gui/src/gui_icons.h` - 添加新图标定义

### 2. 新增的界面处理器
```c
// QR code form screen
static gui_scr_handle_t gui_qr_code_scr_handle = {
    .scr_id = GUI_QR_CODE_SCR_ID,
    .scr_create = qr_code_scr_create,
    .scr_enter = qr_code_scr_enter,
    .scr_exit = qr_code_scr_exit,
};

// Updating form screen
static gui_scr_handle_t gui_updating_scr_handle = {
    .scr_id = GUI_UPDATING_SCR_ID,
    .scr_create = updating_scr_create,
    .scr_enter = updating_scr_enter,
    .scr_exit = updating_scr_exit,
};
```

### 3. 界面切换逻辑
在按钮事件处理函数中添加了新的界面切换逻辑：
- QR码界面：仅在WiFi配置存在时显示
- 更新界面：仅在系统更新时显示（检查`/tmp/system_updating`文件）

### 4. 依赖项
- LVGL QR码库：`lvgl/extra/libs/qrcode/lv_qrcode.h`
- 现有的GUI框架和工具函数
- nvram配置访问函数

## 使用方法

### 1. 触发QR码界面
- 通过按钮循环切换到QR码界面
- 前提条件：WiFi已配置（存在SSID）

### 2. 触发更新界面
- 系统更新时自动显示
- 或通过创建`/tmp/system_updating`文件手动触发

### 3. 界面退出
- 两个界面都会在按钮按下时自动切换到下一个界面
- 或在特定条件下自动跳过

## 技术特点

### 1. 内存管理
- 正确释放nvram_get()返回的内存
- 使用strdup()创建字符串副本
- 及时清理LVGL对象

### 2. 错误处理
- 检查空指针和无效状态
- 优雅处理WiFi未配置的情况
- 提供默认显示内容

### 3. 多语言支持
- 基于ctx->language动态切换文本
- 支持英语(GUI_ICON_LANG_EN)和西班牙语(GUI_ICON_LANG_ES)

### 4. 界面一致性
- 遵循现有界面的设计模式
- 使用相同的创建/进入/退出函数结构
- 保持与其他界面的风格一致

## 注意事项

1. QR码功能需要LVGL QR码库支持
2. 更新界面依赖系统更新状态文件
3. 界面切换逻辑已集成到现有的按钮处理流程中
4. 所有新功能都向后兼容现有系统
