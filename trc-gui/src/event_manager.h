/***********************************************************************************************
 *
 *    Filename: event_manager.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:19:14 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __EVENT_MANAGER_H__
#define __EVENT_MANAGER_H__

#include <stdio.h>
#include <cjson/cJSON.h>

#include "gui_util.h"
#include "gui_comm.h"

struct event_context
{
    pthread_mutex_t lock;
};

struct event_context *event_init(void);
int event_deinit(struct event_context *ctx);


void evhd_data_limit(const char *event, cJSON *data);
void evhd_sys_factreset(const char *event, cJSON *data);
void evhd_sys_poweroff(const char *event, cJSON *data);
void evhd_sys_sleep(const char *event, cJSON *data);
void evhd_sys_wakeup(const char *event, cJSON *data);
void evhd_sys_active(const char *event, cJSON *data);
void evhd_btn_press(const char *event, cJSON *data);
void evhd_btn_short(const char *event, cJSON *data);
void evhd_usb_plug(const char *event, cJSON *data);
void evhd_batt_warn(const char *event, cJSON *data);
void evhd_batt_lv(const char *event, cJSON *data);
void evhd_temp_warm(const char *event, cJSON *data);
void evhd_temp_charge(const char *event, cJSON *data);
void evhd_dm_rtc_alarm(const char *event, cJSON *data);
void evhd_dm_charge_state(const char *event, cJSON *data);


#endif /* __EVENT_MANAGER_H__ */

