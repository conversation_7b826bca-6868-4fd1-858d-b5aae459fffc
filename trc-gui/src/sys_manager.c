/***********************************************************************************************
 *
 *    Filename: sys_manager.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:20:14 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/select.h>

#include "sys_manager.h"
#include "ubus_helper.h"
#include "cJSON.h"
#include "event_manager.h"

struct event_context *g_pevent_ctx = NULL;
struct win_context *g_pwin_ctx = NULL;

// Define the function pointer type for our event handlers
typedef void (*event_handler_t)(const char *event, cJSON *data);

// Define a structure to map event names to handler functions
typedef struct {
    const char *event_name;
    event_handler_t handler;
} event_handler_map_t;


// Map event names to their corresponding handler functions
// The handler functions are now implemented in event_manager.c
static const event_handler_map_t g_event_handlers[] = {
    {"system.factory_reset", evhd_sys_factreset},
    {"system.poweroff", evhd_sys_poweroff},
    {"system.sleep", evhd_sys_sleep},
    {"system.wakeup", evhd_sys_wakeup},
    {"system.active", evhd_sys_active},
    {"button.press", evhd_btn_press},
    {"button.short", evhd_btn_short},
    {"usb.plug", evhd_usb_plug},
    {"battery.warn", evhd_batt_warn},
    {"battery.level", evhd_batt_lv},
};
// ubus call gui notify '{"event": "button.press", "data": {"duration": 100}}'
// This is the central dispatcher that ubus_helper will call
static void system_manager_event_dispatcher(const char *event, cJSON *data)
{
    int i;
    int num_handlers = ARRAY_SIZE(g_event_handlers);

    GUI_DBG("Dispatcher received event: %s", event);

    for (i = 0; i < num_handlers; i++) {
        if (strcmp(g_event_handlers[i].event_name, event) == 0) {
            if (g_event_handlers[i].handler) {
                g_event_handlers[i].handler(event, data);
            }
            return;
        }
    }

    GUI_DBG("No handler found for event: %s", event);
}


gui_retcode_t system_manager_start(void)
{
    int ubus_fd;
    fd_set fds;
    int ret;

    GUI_DBG("---Enter---");

    ubus_fd = ubus_helper_get_fd();
    if (ubus_fd < 0) {
        GUI_ERR("Failed to get ubus file descriptor.");
        return RET_ERROR;
    }

    while (1)
    {
        FD_ZERO(&fds);
        FD_SET(ubus_fd, &fds);

        ret = select(ubus_fd + 1, &fds, NULL, NULL, NULL);

        if (ret < 0) {
            if (errno == EINTR) {
                continue;
            }
            GUI_ERR("select() failed: %s", strerror(errno));
            break;
        }

        if (FD_ISSET(ubus_fd, &fds)) {
            GUI_DBG("Ubus event pending, handling...");
            ubus_helper_handle_events();
        }
    }

    return RET_ERROR; // Should not be reached in normal operation
}

void system_manager_deinit(void)
{
    ubus_helper_deinit();

    win_deinit(g_pwin_ctx);

    event_deinit(g_pevent_ctx);

    return;
}

gui_retcode_t system_manager_init(void)
{
    gui_retcode_t ret = 0;
    GUI_DBG("---Enter---");

    /*log init*/
    ret = log_init("GUI");

    /*ubus helper init*/
    if (ubus_helper_init(system_manager_event_dispatcher) != 0) {
        GUI_ERR("ubus helper init failed");
        return RET_ERROR;
    }

    return RET_OK;
}

gui_retcode_t system_manager_submods_init()
{
    GUI_DBG("---Enter---");

    g_pevent_ctx = event_init();
    if (NULL == g_pevent_ctx)
    {
        GUI_ERR("event init failed");
        return -1;
    }

    g_pwin_ctx = win_init();
    if (NULL == g_pwin_ctx)
    {
        GUI_ERR("windows init failed");
        return -1;
    }

    return 0;
}
