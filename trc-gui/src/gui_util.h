/***********************************************************************************************
 *
 *    Filename: gui_util.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:22:12 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __GUI_UTIL_H__
#define __GUI_UTIL_H__

#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <pthread.h>
#include <sys/sysinfo.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdint.h>
#include <signal.h>
#include <sys/inotify.h>
#include <linux/input.h>

#define REFRESH_TIMER_FREQ  (1)  /* 1 seconds */
#define IDLE_TIMER_FREQ     (20) /* 20 seconds */
#define NOTICE_TIMER_FREQ   (3)  /* 3 seconds */
#define PROGRESS_TIMER_FREQ (3)  /* 3 seconds */

typedef void (*timer_cb_fcn)(union sigval v);

struct util_thelper
{
    unsigned char   thread_exit;
    unsigned char   thread_ready;
    pthread_cond_t  thread_cond;
    pthread_mutex_t thread_mutex;
    pthread_t       thread_id;
    void            *thread_context;
    int             (*thread_proc_init)(void *context);
    int             (*thread_proc_pre)(void *context);
    int             (*thread_proc)(void *context);
    int             (*thread_proc_post)(void *context);
};

typedef enum
{
    UPDATE_TIMEOUT_TIME,
    IDLE_TIMEOUT_EVENT,
    DISP_IDLE_TIMEOUT_EVENT,
    BUTTON_EVENT,
    USB_EVENT,
    SYS_SLEEP_EVENT,
    SYS_ACTIVE_EVENT,
    SYS_WAKEUP_EVENT,
    SYS_POWEROFF_EVENT,
    SYS_RESET_EVENT,
    DATA_LIMIT_EVENT,
    EVENT_END,
}e_msg_type;

typedef struct gui_button
{
    char btn_name[32];
} gui_button_t;

typedef struct usb_action
{
    int action;
} usb_action_t;

typedef struct gui_msg
{
    size_t msg_size;
    e_msg_type msg_type;
    union
    {
        gui_button_t butt_msg;
        usb_action_t usb_msg;
    } msg_data;
} gui_msg_t;

time_t get_uptimes(void);

#define util_launch_thelper(helper, init, pre, proc, post, context) \
    util_launch_thelper_w_name(helper, init, pre, proc, post, context, #proc)

int util_launch_thelper_w_name(struct util_thelper *thelper,
                              int (*thread_proc_init)(void *context),
                              int (*thread_proc_pre)(void *context),
                              int (*thread_proc)(void *context),
                              int (*thread_proc_post)(void *context),
                              void *context,
                              char *name);
int util_unblock_thelper(struct util_thelper *thelper);
int util_join_thelper(struct util_thelper *thelper);

int util_timer_init(void *ctx, timer_t *timerid, timer_cb_fcn handler);
int util_timer_deinit(timer_t timerid);
int util_timer_start(timer_t timerid, int repeat, time_t freq_nanosecs);
int util_timer_stop(timer_t timerid);
void util_send_response(char *req_msg, int msg_id, const void *rsp_data, size_t len);
int util_send_request(int module_id, int msg_id, const void *data, size_t len);

int util_msgget(const char *path, int mode);
int util_msgremove(const char *path, int msgqid);
int util_msgsnd(int msgqid, const void *msgp, size_t msgsz);
int util_msgrcv(int msgqid, void *msgp, size_t msgsz);

int file_exists(const char *path);
int dir_exists(const char *path);
int file_write(const char *filename, const char *format, ...);

int32_t nvram_set(char *name, const char *value);
char *nvram_get(char *name);
int32_t nvram_getint(char *name);
char *tmpnvram_get(char *name);
int32_t tmpnvram_getint(char *name);

int system_call(const char *command);
#endif /* __GUI_UTIL_H__ */

