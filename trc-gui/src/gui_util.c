/***********************************************************************************************
 *
 *    Filename: gui_util.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:22:22 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/
#include <stdio.h>
#include <stdarg.h>

#include "gui_comm.h"
#include "gui_util.h"
#include "ubus_helper.h"

#define _GNU_SOURCE
#define MAX_TASK_COMM_LEN 16

time_t get_uptimes(void)
{
    struct sysinfo info;
    sysinfo(&info);
    return info.uptime;
}

static int thelper_signal_ready(struct util_thelper *thelper)
{
    int result = -1;

    pthread_mutex_lock(&thelper->thread_mutex);
    thelper->thread_ready = 1;
    result = pthread_cond_signal(&thelper->thread_cond);
    pthread_mutex_unlock(&thelper->thread_mutex);

    return result;
}

static int thelper_signal_init(struct util_thelper *thelper)
{
    int result;
    thelper->thread_ready = 0;
    result = pthread_cond_init(&thelper->thread_cond, NULL);
    if (result)
    {
        return result;
    }

    result = pthread_mutex_init(&thelper->thread_mutex, NULL);
    if (result)
    {
        pthread_cond_destroy(&thelper->thread_cond);
    }
    return result;
}

static int thelper_signal_destroy(struct util_thelper *thelper)
{
    int result, ret_result = 0;
    result = pthread_cond_destroy(&thelper->thread_cond);
    if (result)
    {
        ret_result = result;
    }

    result = pthread_mutex_destroy(&thelper->thread_mutex);
    if (result)
    {
        ret_result = result;
    }

    return ret_result;
}

static void *thelper_main(void *data)
{
    int result = 0;
    struct util_thelper *thelper = (struct util_thelper *)data;

    if (thelper->thread_proc_init)
    {
        result = thelper->thread_proc_init(thelper->thread_context);
        if (result < 0)
        {
            thelper->thread_exit = 1;
            thelper_signal_ready(thelper);
            GUI_ERR("error: 0x%lx", (long)thelper);
            return NULL;
        }
    }

    thelper_signal_ready(thelper);
    if (thelper->thread_proc_pre)
    {
        result = thelper->thread_proc_pre(thelper->thread_context);
        if (result < 0)
        {
            thelper->thread_exit = 1;
            GUI_ERR("error: 0x%lx", (long)thelper);
            return NULL;
        }
    }

    do
    {
        if (thelper->thread_proc)
        {
            result = thelper->thread_proc(thelper->thread_context);
            if (result < 0)
            {
                thelper->thread_exit = 1;
                GUI_ERR("error: 0x%lx", (long)thelper);
            }
        }
    } while (thelper->thread_exit == 0);

    if (thelper->thread_proc_post)
    {
        result = thelper->thread_proc_post(thelper->thread_context);
    }

    if (result != 0)
    {
        GUI_ERR("error: 0x%lx", (long)thelper);
    }
    return NULL;
}

static int thelper_signal_wait(struct util_thelper *thelper)
{
    int result = -1;

    pthread_mutex_lock(&thelper->thread_mutex);
    if (!thelper->thread_ready)
    {
        result = pthread_cond_wait(&thelper->thread_cond, &thelper->thread_mutex);
    }
    pthread_mutex_unlock(&thelper->thread_mutex);

    return result;
}

int util_launch_thelper_w_name(struct util_thelper *thelper,
                               int (*thread_proc_init)(void *context),
                               int (*thread_proc_pre)(void *context),
                               int (*thread_proc)(void *context),
                               int (*thread_proc_post)(void *context),
                               void *context,
                               char *name)
{

    int result = -1;
    char lname[MAX_TASK_COMM_LEN + 1] = {0};

    thelper->thread_exit = 0;
    thelper_signal_init(thelper);

    if (context)
    {
        thelper->thread_context = context;
    }

    thelper->thread_proc_init = thread_proc_init;
    thelper->thread_proc_pre = thread_proc_pre;
    thelper->thread_proc = thread_proc;
    thelper->thread_proc_post = thread_proc_post;

    result = pthread_create(&thelper->thread_id, NULL, thelper_main, (void *)thelper);

    if (result != 0)
    {
        GUI_ERR("error 0x%lx\n", (long)thelper);
        return -1;
    }

    if (NULL != name)
    {
        memcpy(lname, name, MAX_TASK_COMM_LEN);
        lname[MAX_TASK_COMM_LEN] = 0;

        // result = pthread_setname_np(thelper->thread_id, lname);
        // if (0 != result)
        // {
        //     GUI_ERR("setname failure - %d\n", result);
        // }
    }

    thelper_signal_wait(thelper);
    return thelper->thread_exit;
}

int util_unblock_thelper(struct util_thelper *thelper)
{
    thelper->thread_exit = 1;
    return 0;
}

int util_join_thelper(struct util_thelper *thelper)
{
    int result = -1;

    result = pthread_join(thelper->thread_id, NULL);
    if (result != 0)
    {
        GUI_ERR("0x%lx", (long)thelper);
    }

    thelper_signal_destroy(thelper);
    return result;
}

int util_timer_init(void *ctx, timer_t *timerid, timer_cb_fcn handler)
{
    struct sigevent sev;

    /* must clear, otherwise may cause timer_create crash */
    memset(&sev, 0, sizeof(sev));

    sev.sigev_notify = SIGEV_THREAD;
    sev.sigev_notify_function = handler;
    sev.sigev_value.sival_ptr = ctx;
    if (timer_create(CLOCK_REALTIME, &sev, timerid) == -1)
    {
        return -1;
    }
    return 0;
}

int util_timer_deinit(timer_t timerid)
{
    if (timerid <= 0)
    {
        return -1;
    }

    return timer_delete(timerid);
}

int util_timer_start(timer_t timerid, int repeat, time_t freq_secs)
{
    struct itimerspec its;

    memset(&its, 0, sizeof(its));
    its.it_value.tv_sec = freq_secs;
    if (repeat)
    {
        its.it_interval.tv_sec = freq_secs;
    }

    return timer_settime(timerid, 0, &its, NULL);
}

int util_timer_stop(timer_t timerid)
{
    struct itimerspec its;

    memset(&its, 0, sizeof(its));

    return timer_settime(timerid, 0, &its, NULL);
}

int32_t nvram_set(char *name, const char *value)
{
    if (!name || !value)
        return -1;

    return uci_setvalue(name, value);
}
char *nvram_get(char *name)
{
    if (!name)
        return NULL;

    return uci_getvalue(name);
}
int32_t nvram_getint(char *name)
{
    if (!name)
        return 0;

    return uci_get_int32(name);
}

char *tmpnvram_get(char *name)
{
    if (!name)
        return NULL;

    return uci_getvalue_tmp(name);
}

int32_t tmpnvram_getint(char *name)
{
    if (!name)
        return 0;

    return uci_get_int32_tmp(name);
}

int util_send_request(int module_id, int msg_id, const void *data, size_t len)
{
    int ret = IPC_OK;
    char *buff = NULL;
    char *msg_data = NULL;
    ipc_addr_t dest = {0};

    /* get ipc addr of dest module */
    ret = ipc_get_addr_by_id(module_id, IPC_MSG_PRI_COMM, &dest);
    if (IPC_OK != ret)
    {
        return -1;
    }

    /* malloc buffer for request */
    buff = IPC_MSG_MALLOC(len);
    if (NULL == buff)
    {
        return -1;;
    }

    /* fill msg header */
    IPC_MSG_FILL_COMM_HDR(buff, msg_id, len);

    /* copy data to msg */
    if(NULL != data && len > 0)
    {
        msg_data = IPC_MSG_GET_DATA(buff);
        if(NULL != msg_data)
        {
            memcpy(msg_data, data, len);
        }
    }

    /* send msg */
    ipc_msg_send_asy(&dest, (unsigned char *)buff, len);
    free(buff);

    return 0;
}

void util_send_response(char *req_msg, int msg_id, const void *rsp_data, size_t len)
{
    char *buff = NULL;
    char *msg_data = NULL;
    ipc_addr_t *src = NULL;

    /* get the source addr of the request */
    src = IPC_MSG_GET_SRC_ADDR(req_msg);
    if (NULL == src)
    {
        return;
    }

    /* malloc a buffer for response */
    buff = IPC_MSG_MALLOC(len);
    if (NULL == buff)
    {
        return;
    }

    /* fill the response msg */
    IPC_MSG_FILL_RSP_HDR(buff, msg_id, len, req_msg);

    /* copy response data to msg */
    if(NULL != rsp_data && 0 != len)
    {
        msg_data = IPC_MSG_GET_DATA(buff);
        if(NULL != msg_data)
        {
            memcpy(msg_data, rsp_data, len);
        }
    }

    /* response it */
    ipc_msg_send_asy(src, (unsigned char *)buff, len);

    free(buff);
    return;
}

static int util_pipeget(const char *pipe_name, int mode)
{
    int fd;
    int result;

    result = mkfifo(pipe_name, 0660);

    if ((-1 == result) && (EEXIST != errno))
    {
        GUI_ERR("pipe_name: %s failed: %d", pipe_name, errno);
        return result;
    }

    result = chmod(pipe_name, 0660);
    if (result != 0)
    {
        GUI_ERR("failed to change mode for %s, error = %s", pipe_name, strerror(errno));
    }

    fd = open(pipe_name, mode);
    if (fd <= 0)
    {
        GUI_ERR("pipe_name: %s failed: %d", pipe_name, errno);
    }

    return fd;
}

static int util_piperemove(const char *pipe_name, int fd)
{
    if (fd > 0)
    {
        close(fd);
    }
    if (pipe_name)
    {
        unlink(pipe_name);
    }

    return 0;
}

static int util_pipewrite(int fd, const void *buf, size_t sz)
{
    return write(fd, buf, sz);
}

static int util_piperead(int fd, void * buf, size_t sz)
{
    return read(fd, buf, sz);
}

int util_msgget(const char *path, int mode)
{
    return util_pipeget(path, mode);
}

int util_msgremove(const char *path, int msgqid)
{
    return util_piperemove(path, msgqid);
}

struct msgbuf
{
    size_t msg_size;
};

int util_msgsnd(int msgqid, const void *msgp, size_t msgsz)
{
    int result;
    struct msgbuf *pmsg = (struct msgbuf *)msgp;
    pmsg->msg_size = msgsz;

    result = util_pipewrite(msgqid, msgp, msgsz);
    if (result != (int)msgsz)
    {
        GUI_ERR("pipe broken %d, msgsz = %d", result, (int)msgsz);
        return -1;
    }

    return result;
}

int util_msgrcv(int msgqid, void *msgp, size_t msgsz)
{
    int len;
    struct msgbuf *pmsg = (struct msgbuf *)msgp;

    len = util_piperead(msgqid, &(pmsg->msg_size), sizeof(pmsg->msg_size));
    if (len != sizeof(pmsg->msg_size))
    {
        GUI_ERR("pipe broken %d", len);
        return -1;
    }

    if (msgsz < pmsg->msg_size)
    {
        GUI_ERR("msgbuf is too small %d < %d", (int)msgsz, (int)pmsg->msg_size);
        return -1;
    }

    len = util_piperead(msgqid, (char *)msgp + sizeof(pmsg->msg_size),
                        pmsg->msg_size - sizeof(pmsg->msg_size));
    if (len != (int)(pmsg->msg_size - sizeof(pmsg->msg_size)))
    {
        GUI_ERR("pipe broken %d, msgsz = %d", len, (int)pmsg->msg_size);
        return -1;
    }

    return pmsg->msg_size;
}

int file_exists(const char *path)
{
    struct stat st;
    return (stat(path, &st) == 0) && (!S_ISDIR(st.st_mode));
}

int dir_exists(const char *path)
{
    struct stat st;
    return (stat(path, &st) == 0) && (S_ISDIR(st.st_mode));
}

int file_write(const char *filename, const char *format, ...)
{
    va_list args;
    FILE *fp = fopen(filename, "w");
    if(NULL == fp)
    {
        return -1;
    }

    va_start(args, format);
    vfprintf(fp, format, args);
    va_end(args);
    fclose(fp);

    return 0;
}

int system_call(const char *command)
{
    int result = -1;
    FILE *stream = NULL;

    stream = popen(command, "w");
    if (stream == NULL)
    {
        result = -1;
    }
    else
    {
        result = pclose(stream);
        if (WIFEXITED(result))
        {
            result = WEXITSTATUS(result);
        }
    }

    return result;
}
