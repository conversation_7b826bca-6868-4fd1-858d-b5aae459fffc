/***********************************************************************************************
 *
 *    Filename: trc_daemon.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:21:16 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#include <stdio.h>

#include "gui_comm.h"
#include "gui_util.h"
#include "sys_manager.h"

int main(int argc, char *argv[])
{
    GUI_DBG("start gui daemon")

    if (mkdir(WIN_PATH, 0755) && errno != EEXIST)
    {
        GUI_ERR("can not create dir '%s'", WIN_PATH);
        return -1;
    }

    if (0 != system_manager_init())
    {
        GUI_ERR("system manager init error.");
        return -1;
    }

    if (0 != system_manager_submods_init())
    {
        GUI_ERR("system manager init error.");
        system_manager_deinit();
        return -1;
    }

    if (0 != system_manager_start())
    {
        GUI_ERR("system manager start error.");
        goto END;
    }

    return 0;

END:
    system_manager_deinit();
    return -1;
}
