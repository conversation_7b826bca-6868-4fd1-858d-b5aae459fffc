/***********************************************************************************************
 *
 *    Filename: sys_manager.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:20:23 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __SYS_MANAGER_H__
#define __SYS_MANAGER_H__

#include <stdio.h>
#include <stdlib.h>

#include "gui_comm.h"
#include "event_manager.h"
#include "win_manager.h"

typedef gui_retcode_t (*system_manager_exev_handle)(char *msg);
typedef gui_retcode_t (*system_manager_inev_init)(void *param);

gui_retcode_t system_manager_init(void);
gui_retcode_t system_manager_submods_init(void);
void system_manager_deinit(void);
gui_retcode_t system_manager_start(void);

#endif /* __SYS_MANAGER_H__ */

