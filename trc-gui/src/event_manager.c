/***********************************************************************************************
 *
 *    Filename: event_manager.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:19:22 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#include <stdio.h>
#include <poll.h>
#include <linux/input.h>
#include "cJSON.h"

#include "event_manager.h"
#include "win_manager.h"
#include "gui_comm.h"
#include "dm_api.h"
#include "gui_icons.h"

static int g_gui_state = GUI_INIT_STATE;

// Helper function to get a string from a cJSON object
static const char* get_json_string(cJSON *json, const char *key) {
    if (!json) return NULL;
    cJSON *item = cJSON_GetObjectItem(json, key);
    if (cJSON_IsString(item) && (item->valuestring != NULL)) {
        return item->valuestring;
    }
    return NULL;
}

// Helper function to get an integer from a cJSON object
static int get_json_int(cJSON *json, const char *key, int default_val) {
    if (!json) return default_val;
    cJSON *item = cJSON_GetObjectItem(json, key);
    if (cJSON_IsNumber(item)) {
        return item->valueint;
    }
    return default_val;
}

struct event_context *event_init(void)
{
    struct event_context *ctx = NULL;
    ctx = calloc(1, sizeof(*ctx));
    if (!ctx)
    {
        GUI_ERR("calloc ctx failed, quit");
        return NULL;
    }
    pthread_mutex_init(&ctx->lock, NULL);

    g_gui_state = GUI_ACTIVE_STATE;

    GUI_DBG("event_init finished");
    return ctx;
}

int event_deinit(struct event_context *ctx)
{
    if (NULL == ctx)
    {
        return 0;
    }

    free(ctx);

    return 0;
}

void evhd_btn_short(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    const char *btn_name = NULL;

    GUI_DBG("---Enter event: %s---", event);

    if (GUI_ACTIVE_STATE != g_gui_state)
    {
        GUI_DBG("gui state is not active [%d]", g_gui_state);
        return;
    }

    btn_name = get_json_string(data, "btn_name");
    if(btn_name)
    {
        GUI_DBG("btn_name = %s", btn_name);
        gui_msg.msg_type = BUTTON_EVENT;
        snprintf(gui_msg.msg_data.butt_msg.btn_name, sizeof(gui_msg.msg_data.butt_msg.btn_name),
                 "%s_PRESS", btn_name);
        win_message_event(&gui_msg);
    }
}

void evhd_btn_press(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    const char *btn_name = NULL;

    GUI_DBG("---Enter event: %s---", event);

    if (GUI_ACTIVE_STATE != g_gui_state)
    {
        GUI_DBG("gui state is not active [%d]", g_gui_state);
        return;
    }

    btn_name = get_json_string(data, "btn_name");
    if(btn_name)
    {
        GUI_DBG("btn_name = %s", btn_name);
        if (strcasecmp("BUTTON_RESET", btn_name) == 0)
        {
            gui_msg.msg_type = SYS_RESET_EVENT;
            win_message_event(&gui_msg);
            g_gui_state = GUI_RESET_STATE;
        } else {
             GUI_DBG("ignore button action: %s", btn_name);
        }
    }
}

void evhd_usb_plug(const char *event, cJSON *data)
{
    GUI_DBG("---Enter event: %s---", event);
    int action = get_json_int(data, "action", -1);

    GUI_DBG("usb action = %d", action);
    if (0 == action)
    {
        file_write(USB_CONN_FILE, "");
        unlink(BATT_LOW_FILE);
    }
    else if (1 == action)
    {
        unlink(USB_CONN_FILE);
        unlink(USB_CONN48_HOUR_FILE);
        unlink(USB_CONN120_HOUR_FILE);
    }
}

void evhd_batt_warn(const char *event, cJSON *data)
{
    GUI_DBG("---Enter event: %s---", event);
    int warn_lv = get_json_int(data, "warn_lv", -1);

    GUI_DBG("battery warning level = %d", warn_lv);
    if (0 == warn_lv)
    {
        file_write(BATT_LOW_FILE, "");
    }
}

void evhd_batt_lv(const char *event, cJSON *data)
{
    GUI_DBG("---Enter event: %s---", event);
    int cap_lv = get_json_int(data, "cap_lv", -1);

    GUI_DBG("battery level = %d", cap_lv);
    if (cap_lv >= 10)
    {
        file_write(BATT_FULL_FILE, "");
    }
}

void evhd_sys_sleep(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    GUI_DBG("---Enter event: %s---", event);
    gui_msg.msg_type = SYS_SLEEP_EVENT;
    win_message_event(&gui_msg);
    g_gui_state = GUI_SLEEP_STATE;
}

void evhd_sys_active(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    GUI_DBG("---Enter event: %s---", event);
    gui_msg.msg_type = SYS_ACTIVE_EVENT;
    win_message_event(&gui_msg);
    g_gui_state = GUI_ACTIVE_STATE;
}

void evhd_sys_wakeup(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    GUI_DBG("---Enter event: %s---", event);
    gui_msg.msg_type = SYS_WAKEUP_EVENT;
    win_message_event(&gui_msg);
    g_gui_state = GUI_WAKEUP_STATE;
}

void evhd_sys_poweroff(const char *event, cJSON *data)
{
    gui_msg_t gui_msg = {0};
    GUI_DBG("---Enter event: %s---", event);
    gui_msg.msg_type = SYS_POWEROFF_EVENT;
    win_message_event(&gui_msg);
    g_gui_state = GUI_POWEROFF_STATE;
}
