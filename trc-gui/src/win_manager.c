/***********************************************************************************************
 *
 *    Filename: win_manager.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:18:46 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
 **********************************************************************************************/
#include <stdio.h>
#include <signal.h>
#include <poll.h>

#include "gui_comm.h"
#include "gui_icons.h"
#include "win_manager.h"
#include "sms_data.h"

#include "lvgl_object.h"
#include "lvgl/extra/libs/qrcode/lv_qrcode.h"

#define FB_DEV         "/dev/fb0"
#define GUI_EVENT_PATH "/tmp/.gui_event_q"

/******************************************************************************
 *   Global variables
 ******************************************************************************/
extern struct gui_icon *gui_icons[];
extern size_t gui_num_icons;
extern char g_alert_path[MAX_ALERT_PATH][256];

enum
{
    GUI_MIAN_SCR_ID = 0,
    GUI_WIFI_INFO_SCR_ID,
    GUI_SMS_LIST_SCR_ID,
    GUI_QR_CODE_SCR_ID,
    GUI_UPDATING_SCR_ID,
    GUI_POWEROFF_SCR_ID,
    GUI_MAX_SCR_ID
};

typedef struct _scr_icons_t
{
    const char *icons_name;
    int index;
} scr_icons_t;

static void refresh_sms_list_view(lv_obj_t* parent, int page, struct win_context *ctx)
{
    // Define layout constants
    const lv_coord_t LEFT_COLUMN_X = 10;
    const lv_coord_t RIGHT_COLUMN_X = 140;
    const lv_coord_t START_Y = 40;
    const lv_coord_t ROW_HEIGHT = 22;
    const int ITEMS_PER_PAGE = 10;

    // 1. Clear old list items from the parent container
    lv_obj_clean(parent);

    // 2. Get data for the current page (always page 0)
    sms_message_t messages[ITEMS_PER_PAGE];
    int msg_count_on_page = sms_get_messages_for_page(0, messages, ITEMS_PER_PAGE);

    // 3. Draw the left column (items 1-5)
    for (int i = 0; i < (ITEMS_PER_PAGE / 2); i++) {
        if (i >= msg_count_on_page) break;

        lv_coord_t current_y = START_Y + (i * ROW_HEIGHT);
        int item_index = (page * ITEMS_PER_PAGE) + i + 1;

        // Create item number label
        lv_obj_t *label_num = lv_label_create(parent);
        lv_label_set_text_fmt(label_num, "%d", item_index);
        lv_obj_set_pos(label_num, LEFT_COLUMN_X, current_y);

        // Create message summary label
        lv_obj_t *label_text = lv_label_create(parent);
        lv_label_set_text(label_text, messages[i].phone_number);
        lv_obj_align_to(label_text, label_num, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

        // Create underline
        lv_obj_t *line = lv_obj_create(parent);
        lv_obj_set_size(line, 120, 1);
        lv_obj_set_style_bg_color(line, lv_color_hex(0x444444), 0);
        lv_obj_set_style_border_width(line, 0, 0);
        lv_obj_align_to(line, label_num, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 3);
    }

    // 4. Draw the right column (items 6-10)
    for (int i = 0; i < (ITEMS_PER_PAGE / 2); i++) {
        int msg_array_index = i + (ITEMS_PER_PAGE / 2);
        if (msg_array_index >= msg_count_on_page) break;

        lv_coord_t current_y = START_Y + (i * ROW_HEIGHT);
        int item_index = (page * ITEMS_PER_PAGE) + msg_array_index + 1;

        // Create item number label
        lv_obj_t *label_num = lv_label_create(parent);
        lv_label_set_text_fmt(label_num, "%d", item_index);
        lv_obj_set_pos(label_num, RIGHT_COLUMN_X, current_y);

        // Create message summary label
        lv_obj_t *label_text = lv_label_create(parent);
        lv_label_set_text(label_text, messages[msg_array_index].phone_number);
        lv_obj_align_to(label_text, label_num, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

        // Create underline
        lv_obj_t *line = lv_obj_create(parent);
        lv_obj_set_size(line, 120, 1);
        lv_obj_set_style_bg_color(line, lv_color_hex(0x444444), 0);
        lv_obj_set_style_border_width(line, 0, 0);
        lv_obj_align_to(line, label_num, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 3);
    }
}


static void scrs_init(struct win_context *ctx);
static void scrs_deinit(void);
static int gui_icons_deinit(size_t cnt, scr_icons_t *icons);
static int gui_icons_init(void *dfb, size_t cnt, scr_icons_t *icons, void *parent);
static int gui_icons_update(void *dfb, size_t cnt, scr_icons_t *icons);
static bool gui_stack_scr_switch(uint32_t scr_id);
static gui_scr_handle_t *gui_scr_find_by_id(uint32_t scr_id);

static int32_t g_msg_q_id;

static gui_scr_handle_list_t *g_reg_scr_list = NULL;

static int32_t g_win_state = GUI_INIT_STATE;


static int do_disp_idle_event(struct win_context *ctx)
{
    if (NULL == ctx)
    {
        GUI_ERR("input context is NULL");
        return -1;
    }

    GUI_DBG("===window display idle timeout");
    gui_stack_scr_switch(GUI_MIAN_SCR_ID);

    return 0;
}

static int do_idle_event(struct win_context *ctx)
{
    if (NULL == ctx)
    {
        GUI_ERR("input context is NULL");
        return -1;
    }

    GUI_DBG("===window idle timeout");
    ctx->gui_state = gui_screen_power(eSCR_POWER_OFF);

    return 0;
}

static int wifi_display_check(void)
{
    int retval = 0;
    char *pssid1 = NULL;
    char *pssid2 = NULL;
    char *fact_pssid = NULL;

    retval = nvram_getint("wireless.wlancfg.main_display");
    if (0 == retval)
    {
        GUI_DBG("wifi display switch off");
        return 0;
    }

    retval = 1;
    pssid1 = nvram_get("wireless.@wifi-iface[0].ssid");
    pssid2 = nvram_get("wireless.@wifi-iface[2].ssid");
    fact_pssid = factnvram_get("wlan_ap0_ssid");

    if ((NULL == pssid1 || strlen(pssid1) <= 0) && (NULL == pssid2 || strlen(pssid2) <= 0) &&
        (NULL == fact_pssid || strlen(fact_pssid) <= 0))
    {
        retval = 0;
    }

    if (pssid1)
    {
        free(pssid1);
    }
    if (pssid2)
    {
        free(pssid2);
    }
    if (fact_pssid)
    {
        free(fact_pssid);
    }

    return retval;
}

static int do_button_event(struct win_context *ctx)
{
    int i = 0;
    int scr_index = 0;
    int sim = 0, reg = 0, sig = 0, mask = 0;

    if (NULL == ctx)
    {
        GUI_ERR("input context is NULL");
        return -1;
    }

    /* reset timers */
    GUI_DBG("reset window idle timer [0x%x]", (uint32_t)ctx->idle_timer);
    pthread_mutex_lock(&ctx->lock);
    util_timer_start(ctx->idle_timer, FALSE, ctx->timeout);
    pthread_mutex_unlock(&ctx->lock);
    /* reset timers */
    GUI_DBG("reset lcd display idle timer [0x%x]", (uint32_t)ctx->disp_idle_timer);
    util_timer_start(ctx->disp_idle_timer, FALSE, IDLE_TIMER_FREQ);

    if (file_exists(POWERON_FILE))
    {
        GUI_DBG("gui first power on, do nothing");
        return 0;
    }

    if (GUI_ACTIVE_STATE != g_win_state)
    {
        GUI_DBG("gui status is not active, do nothing");
        return 0;
    }

    scr_index = ctx->screen_index;
    if (eSCR_POWER_OFF == ctx->gui_state)
    {
        GUI_DBG("lcd backlight is off");
        ctx->gui_state = gui_screen_power(eSCR_POWER_ON);
        return 0;
    }

    mask = get_msg_form();
    if (GUI_MIAN_SCR_ID == scr_index && (mask & MAIN_ERROR_ICON))
    {
        sim = tmpnvram_getint("mmtmp.netstatus.sim");
        reg = tmpnvram_getint("mmtmp.netstatus.reg");
        sig = tmpnvram_getint("mmtmp.netstatus.sig");
        if (1 == sim && 2 == reg && sig > 0 && file_exists(BATT_LOW_FILE))
        {
            GUI_DBG("battery low, do nothing");
            unlink(BATT_LOW_FILE);
            return 0;
        }
    }
    else if(mask & MAIN_ALERT_ICON)
    {
    }
    else if (GUI_MIAN_SCR_ID == scr_index)
    {
        for (i = (MAX_ALERT_PATH - 1); i >= 0; i--)
        {
            if (strlen(g_alert_path[i]))
            {
                GUI_DBG("alert path [%s], do nothing", g_alert_path[i]);
                unlink(g_alert_path[i]);
                memset(g_alert_path[i], 0, sizeof(g_alert_path[i]));
                return 0;
            }
        }
    }

    scr_index++;

    if(GUI_WIFI_INFO_SCR_ID == scr_index && 0 == wifi_display_check())
    {
        scr_index++;
    }

    if (GUI_SMS_LIST_SCR_ID == scr_index)
    {
        if (sms_get_total_count() == 0)
        {
            scr_index++;
        }
    }

    // Skip QR code screen if WiFi is not configured
    if (GUI_QR_CODE_SCR_ID == scr_index)
    {
        char *ssid = nvram_get("wireless.@wifi-iface[0].ssid");
        if (!ssid || strlen(ssid) == 0)
        {
            scr_index++;
        }
        if (ssid) free(ssid);
    }

    // Skip updating screen unless system is actually updating
    if (GUI_UPDATING_SCR_ID == scr_index)
    {
        if (!file_exists("/tmp/system_updating"))
        {
            scr_index++;
        }
    }

    if (scr_index >= GUI_QR_CODE_SCR_ID)
    {
        scr_index = GUI_MIAN_SCR_ID;
    }

    GUI_DBG("switch screen index [%d]", scr_index);
    gui_stack_scr_switch(scr_index);
}

static void param_timer_handler(union sigval v)
{
    char *p = NULL;
    int value = 0;
    int timeout = 0;
    struct win_context *ctx = (struct win_context *)v.sival_ptr;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return;
    }

    if (g_msg_q_id <= 0)
    {
        GUI_ERR("message pipe queue error");
        return;
    }

    // p = nvram_get("trc_web.web.language");
    // if (p)
    // {
    //     ctx->language = strcasecmp(p, "en") ? GUI_ICON_LANG_ES : GUI_ICON_LANG_EN;
    //     set_sys_language(ctx->language);
    //     free(p);
    // }

    value = nvram_getint("dm.display.lcdtimeout");
    switch (value)
    {
    case 0:
        timeout = 30;
        break;
    case 1:
        timeout = 60;
        break;
    case 2:
        timeout = 300;
        break;
    default:
        timeout = 0;
        break;
    }

    if(timeout != ctx->timeout)
    {
        GUI_DBG("update lcd timeout time");
        ctx->timeout = timeout;
        ctx->gui_state = gui_screen_power(eSCR_POWER_ON);

        pthread_mutex_lock(&ctx->lock);
        util_timer_start(ctx->idle_timer, FALSE, timeout);
        pthread_mutex_unlock(&ctx->lock);
    }

    return;
}

static void disp_idle_timer_handler(union sigval v)
{
    struct win_context *ctx = (struct win_context *)v.sival_ptr;
    gui_msg_t gui_msg = {0};

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return;
    }

    if (g_msg_q_id <= 0)
    {
        GUI_ERR("message pipe queue error");
        return;
    }

    gui_msg.msg_type = DISP_IDLE_TIMEOUT_EVENT;
    util_msgsnd(g_msg_q_id, &gui_msg, sizeof(gui_msg));

    return;
}

static void idle_timer_handler(union sigval v)
{
    struct win_context *ctx = (struct win_context *)v.sival_ptr;
    gui_msg_t gui_msg = {0};

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return;
    }

    if (g_msg_q_id <= 0)
    {
        GUI_ERR("message pipe queue error");
        return;
    }

    gui_msg.msg_type = IDLE_TIMEOUT_EVENT;
    util_msgsnd(g_msg_q_id, &gui_msg, sizeof(gui_msg));

    return;
}

static int gui_icons_deinit(size_t cnt, scr_icons_t *icons)
{
    int i = 0;
    struct gui_icon *ic = NULL;

    for (i = 0; i < cnt; i++)
    {
        if (icons[i].index < 0)
        {
            GUI_ERR("icons index less than 0");
            continue;
        }

        ic = gui_icons[icons[i].index];
        if (ic->type == GUI_ICON_TYPE_IMAGE)
        {
            image_deinit(ic->h);
        }
        else if (ic->type == GUI_ICON_TYPE_TEXT)
        {
            textbox_deinit(ic->h);
        }
        else if (ic->type == GUI_ICON_TYPE_BAR)
        {
            bar_deinit(ic->h);
        }
    }

    return 0;
}

static int gui_icons_init(void *dfb, size_t cnt, scr_icons_t *icons, void *parent)
{
    int i = 0;
    struct gui_icon *ic = NULL;
    int key = 0;
    int value = 0;
    char *text = NULL;

    GUI_DBG("---Enter---");

    for (i = 0; i < cnt; i++)
    {
        if (icons[i].index < 0)
        {
            GUI_ERR("icons index less than 0");
            continue;
        }

        ic = gui_icons[icons[i].index];
        switch (ic->type)
        {
        case GUI_ICON_TYPE_IMAGE:
            ic->h = image_init(dfb);
            image_setup(ic->h, ic->image.rect, parent);
            image_set_array(ic->h, ic->image.arr, ic->image.arr_size);

            key = ic->image.val_to_key(ic, NULL);
            image_set_key(ic->h, key);
            break;
        case GUI_ICON_TYPE_TEXT:
            ic->h = textbox_init(dfb);
            textbox_setup(ic->h, ic->text.pos, ic->text.rgb, parent);
            text = ic->text.val_to_string(ic, NULL);
            textbox_set_text(ic->h, text);
            // GUI_DBG("textbox string [%s]", text);
            if (text)
                free(text);
            break;
        case GUI_ICON_TYPE_BAR:
            ic->h = bar_init(dfb);
            bar_setup(ic->h, ic->bar.rect, parent);
            value = ic->bar.val_to_bar(ic, NULL);
            bar_set_value(ic->h, value);
            break;
        default:
            GUI_ERR("unsupported icon type");
            break;
        }
    }

    return 0;
}

static int gui_icons_update(void *dfb, size_t cnt, scr_icons_t *icons)
{
    struct gui_icon *ic = NULL;
    int key = 0;
    int value = 0;
    char *text = NULL;

    if (NULL == dfb || NULL == icons)
    {
        GUI_ERR("input parameter is NULL");
        return -1;
    }

    for (size_t i = 0; i < cnt; i++)
    {
        if (icons[i].index < 0)
        {
            GUI_ERR("icons index less than 0");
            continue;
        }

        ic = gui_icons[icons[i].index];
        switch (ic->type)
        {
        case GUI_ICON_TYPE_IMAGE:
            key = ic->image.val_to_key(ic, NULL);
            image_set_key(ic->h, key);
            break;
        case GUI_ICON_TYPE_TEXT:
            text = ic->text.val_to_string(ic, NULL);
            textbox_set_text(ic->h, text);
            if (text)
                free(text);
            break;
        case GUI_ICON_TYPE_BAR:
            value = ic->bar.val_to_bar(ic, NULL);
            bar_set_value(ic->h, value);
            break;
        default:
            GUI_ERR("unsupported icon type");
            break;
        }
    }

    gui_refresh_obj(dfb);

    lvgl_update();

    return 0;
}

/******************************************************************************
 * poweroff scr widgets
 ******************************************************************************/
static int poweroff_scr_create(struct _gui_scr_handle *this, void *parent)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    int i = 0, j = 0;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    if (NULL == ctx->obj)
    {
        GUI_DBG("windows screen object is NULL");
        return -1;
    }

    void *img = lvgl_create_imgobj(ctx->obj);
    lvgl_set_img(img, "f"IMAGEDIR"/power_off.bin");
    lvgl_update();

    return 0;
}


/******************************************************************************
 * main scr widgets
 ******************************************************************************/
static scr_icons_t main_scr_icons[] = {
    {.index = -1, .icons_name = GUI_SIGNAL_ICON,},
    {.index = -1, .icons_name = GUI_BATTERY_ICON,},
    {.index = -1, .icons_name = GUI_SMS_ICON,},
    {.index = -1, .icons_name = GUI_WIFI_USER_ICON,},
    // {.index = -1, .icons_name = GUI_TRAFFIC_CION,},
    {.index = -1, .icons_name = GUI_NET_TYPE_ICON,},
    {.index = -1, .icons_name = GUI_MAIN_ERROR_ICON,},
    {.index = -1, .icons_name = GUI_MAIN_ALERT_ICON,},
    {.index = -1, .icons_name = GUI_MAIN_NOTICE_ICON,},
};

static void main_timer_handler(union sigval v)
{
    struct win_context *ctx = (struct win_context *)v.sival_ptr;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return;
    }

    pthread_mutex_lock(&ctx->lock);
    gui_icons_update(ctx->dfb, ARRAY_SIZE(main_scr_icons), main_scr_icons);
    pthread_mutex_unlock(&ctx->lock);

    return;
}

static int main_scr_create(struct _gui_scr_handle *this, void *parent)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    int i = 0, j = 0;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    if (NULL == ctx->obj)
    {
        GUI_DBG("windows screen object is NULL");
        return -1;
    }

    for (i = 0; i < gui_num_icons; i++)
    {
        for (j = 0; j < ARRAY_SIZE(main_scr_icons); j++)
        {
            if (strcmp(main_scr_icons[j].icons_name, gui_icons[i]->name))
            {
                continue;
            }
            main_scr_icons[j].index = i;
        }
    }
    util_timer_init(ctx, &ctx->clock_timer, main_timer_handler);
    gui_icons_init(ctx->dfb, ARRAY_SIZE(main_scr_icons), main_scr_icons, ctx->obj);

    pthread_mutex_lock(&ctx->lock);
    gui_icons_update(ctx->dfb, ARRAY_SIZE(main_scr_icons), main_scr_icons);
    pthread_mutex_unlock(&ctx->lock);

    return 0;
}

static int main_scr_enter(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    /* start timers */
    pthread_mutex_lock(&ctx->lock);
    util_timer_start(ctx->clock_timer, TRUE, REFRESH_TIMER_FREQ);
    pthread_mutex_unlock(&ctx->lock);

    return 0;
}

static int main_scr_exit(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }
    /* stop timers */
    pthread_mutex_lock(&ctx->lock);
    util_timer_stop(ctx->clock_timer);
    pthread_mutex_unlock(&ctx->lock);
    util_timer_deinit(ctx->clock_timer);

    gui_icons_deinit(ARRAY_SIZE(main_scr_icons), main_scr_icons);

    lvgl_clean_obj(ctx->obj);

    return 0;
}

static int main_scr_message(struct _gui_scr_handle *this, const char *msg)
{
    GUI_DBG("main screen recv event message %s", msg);
    return 0;
}

/******************************************************************************
 * SMS list scr widgets
 ******************************************************************************/

static int sms_scr_create(struct _gui_scr_handle *this, void *parent)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    if (NULL == ctx) {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    // Use the main screen object as the parent for all SMS UI elements
    this->obj = ctx->obj;

    // Create a static title for the SMS screen
    // Placeholder for the envelope icon
    lv_obj_t * icon_img = lv_img_create(this->obj);
    // TODO: Replace with the actual sms.bin path once available
    // lv_img_set_src(icon_img, "f"IMAGEDIR"/sms.bin");
    lv_obj_set_pos(icon_img, 10, 10);

    // Title text (multi-language support)
    lv_obj_t * title_label = lv_label_create(this->obj);
    const char* title = (ctx->language == GUI_ICON_LANG_ES) ? "Mensajes no leídos" : "Unread Messages";
    lv_label_set_text(title_label, title);
    lv_obj_align_to(title_label, icon_img, LV_ALIGN_OUT_RIGHT_MID, 5, 0);

    // Initial draw of the first page
    refresh_sms_list_view(this->obj, 0, ctx);

    return 0;
}

static int sms_scr_enter(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    if (NULL == ctx) {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    // Reset to the first page every time we enter the screen
    ctx->sms_current_page = 0;
    refresh_sms_list_view(this->obj, ctx->sms_current_page, ctx);

    return 0;
}

static int sms_scr_exit(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    if (NULL == ctx) {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    // Clean all objects created on this screen
    lvgl_clean_obj(ctx->obj);

    return 0;
}

static int sms_scr_message(struct _gui_scr_handle *this, const char *msg)
{
    GUI_DBG("sms screen recv event message %s", msg);

    return 0;
}

/******************************************************************************
 * wifi info scr widgets
 ******************************************************************************/
static scr_icons_t wifi_info_scr_icons[] = {
    {.index = -1, .icons_name = GUI_WIFI_NAME_ICON,},
    {.index = -1, .icons_name = GUI_WIFI_SSID_ICON,},
    {.index = -1, .icons_name = GUI_WIFI_PASSWD_ICON,},
    {.index = -1, .icons_name = GUI_WIFI_KEY_ICON,},
};

static void wifi_info_timer_handler(union sigval v)
{
    struct win_context *ctx = (struct win_context *)v.sival_ptr;
    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return;
    }

    pthread_mutex_lock(&ctx->lock);
    gui_icons_update(ctx->dfb, ARRAY_SIZE(wifi_info_scr_icons), wifi_info_scr_icons);
    pthread_mutex_unlock(&ctx->lock);

    return;
}

static int wifi_info_scr_create(struct _gui_scr_handle *this, void *parent)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    int i = 0, j = 0;

    if (NULL == ctx || NULL == ctx->obj)
    {
        GUI_ERR("windows context or object is NULL");
        return -1;
    }

    for (i = 0; i < gui_num_icons; i++)
    {
        for (j = 0; j < ARRAY_SIZE(wifi_info_scr_icons); j++)
        {
            if (strcmp(wifi_info_scr_icons[j].icons_name, gui_icons[i]->name))
            {
                continue;
            }
            wifi_info_scr_icons[j].index = i;
        }
    }

    util_timer_init(ctx, &ctx->clock_timer, wifi_info_timer_handler);

    gui_icons_init(ctx->dfb, ARRAY_SIZE(wifi_info_scr_icons), wifi_info_scr_icons, ctx->obj);

    return 0;
}

static int wifi_info_scr_enter(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    pthread_mutex_lock(&ctx->lock);
    gui_icons_update(ctx->dfb, ARRAY_SIZE(wifi_info_scr_icons), wifi_info_scr_icons);
    pthread_mutex_unlock(&ctx->lock);

    /* start timers */
    pthread_mutex_lock(&ctx->lock);
    util_timer_start(ctx->clock_timer, TRUE, REFRESH_TIMER_FREQ);
    pthread_mutex_unlock(&ctx->lock);

    return 0;
}

static int wifi_info_scr_exit(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;
    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    /* stop timers */
    pthread_mutex_lock(&ctx->lock);
    util_timer_stop(ctx->clock_timer);
    pthread_mutex_unlock(&ctx->lock);
    util_timer_deinit(ctx->clock_timer);

    gui_icons_deinit(ARRAY_SIZE(wifi_info_scr_icons), wifi_info_scr_icons);

    lvgl_clean_obj(ctx->obj);

    return 0;
}

/******************************************************************************
 * QR code scr widgets
 ******************************************************************************/
static int qr_code_scr_create(struct _gui_scr_handle *this, void *parent)
{
    struct win_context *ctx = (struct win_context *)this->ctx;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    if (NULL == ctx->obj)
    {
        GUI_DBG("windows screen object is NULL");
        return -1;
    }

    // Create QR code title
    lv_obj_t *title_label = lv_label_create(ctx->obj);
    const char* title = (ctx->language == GUI_ICON_LANG_ES) ? "Código QR" : "QR Code";
    lv_label_set_text(title_label, title);
    lv_obj_set_pos(title_label, 10, 10);

    // Create QR code object
    lv_obj_t *qr_code = lv_qrcode_create(ctx->obj, 150, lv_color_hex(0x000000), lv_color_hex(0xFFFFFF));
    lv_obj_center(qr_code);

    // Get WiFi information for QR code
    char *ssid = nvram_get("wireless.@wifi-iface[0].ssid");
    char *password = nvram_get("wireless.@wifi-iface[0].key");

    // Create WiFi QR code data string
    char qr_data[512];
    if (ssid && password && strlen(ssid) > 0) {
        snprintf(qr_data, sizeof(qr_data), "WIFI:T:WPA;S:%s;P:%s;H:false;;", ssid, password);
    } else if (ssid && strlen(ssid) > 0) {
        snprintf(qr_data, sizeof(qr_data), "WIFI:T:nopass;S:%s;;;", ssid);
    } else {
        snprintf(qr_data, sizeof(qr_data), "No WiFi configured");
    }

    // Update QR code with WiFi data
    lv_qrcode_update(qr_code, qr_data, strlen(qr_data));

    // Create instruction text
    lv_obj_t *instruction_label = lv_label_create(ctx->obj);
    const char* instruction = (ctx->language == GUI_ICON_LANG_ES) ?
        "Escanee para conectar WiFi" : "Scan to connect WiFi";
    lv_label_set_text(instruction_label, instruction);
    lv_obj_align_to(instruction_label, qr_code, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);

    // Clean up allocated memory
    if (ssid) free(ssid);
    if (password) free(password);

    return 0;
}

static int qr_code_scr_enter(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    return 0;
}

static int qr_code_scr_exit(struct _gui_scr_handle *this)
{
    struct win_context *ctx = (struct win_context *)this->ctx;

    if (NULL == ctx)
    {
        GUI_ERR("windows context is NULL");
        return -1;
    }

    // Clean all objects created on this screen
    lvgl_clean_obj(ctx->obj);

    return 0;
}

/******************************************************************************
 * Updating scr widgets
 ******************************************************************************/
// static scr_icons_t updating_scr_icons[] = {
//     {.index = -1, .icons_name = GUI_PROGRESS_ICON,},
// };

// static void updating_timer_handler(union sigval v)
// {
//     struct win_context *ctx = (struct win_context *)v.sival_ptr;

//     if (NULL == ctx)
//     {
//         GUI_ERR("windows context is NULL");
//         return;
//     }

//     pthread_mutex_lock(&ctx->lock);
//     gui_icons_update(ctx->dfb, ARRAY_SIZE(updating_scr_icons), updating_scr_icons);
//     pthread_mutex_unlock(&ctx->lock);

//     return;
// }

// static int updating_scr_create(struct _gui_scr_handle *this, void *parent)
// {
//     struct win_context *ctx = (struct win_context *)this->ctx;
//     int i = 0, j = 0;

//     if (NULL == ctx)
//     {
//         GUI_ERR("windows context is NULL");
//         return -1;
//     }

//     if (NULL == ctx->obj)
//     {
//         GUI_DBG("windows screen object is NULL");
//         return -1;
//     }

//     // Create updating title
//     lv_obj_t *title_label = lv_label_create(ctx->obj);
//     const char* title = (ctx->language == GUI_ICON_LANG_ES) ? "Actualizando..." : "Updating...";
//     lv_label_set_text(title_label, title);
//     lv_obj_center(title_label);
//     lv_obj_set_y(title_label, -20);

//     // Create progress indicator (simple progress bar)
//     lv_obj_t *progress_bar = lv_bar_create(ctx->obj);
//     lv_obj_set_size(progress_bar, 200, 20);
//     lv_obj_center(progress_bar);
//     lv_obj_set_y(progress_bar, 20);
//     lv_bar_set_value(progress_bar, 50, LV_ANIM_ON);

//     // Find and initialize progress icon for additional text display
//     for (i = 0; i < gui_num_icons; i++)
//     {
//         for (j = 0; j < ARRAY_SIZE(updating_scr_icons); j++)
//         {
//             if (strcmp(updating_scr_icons[j].icons_name, gui_icons[i]->name))
//             {
//                 continue;
//             }
//             updating_scr_icons[j].index = i;
//         }
//     }

//     util_timer_init(ctx, &ctx->clock_timer, updating_timer_handler);
//     gui_icons_init(ctx->dfb, ARRAY_SIZE(updating_scr_icons), updating_scr_icons, ctx->obj);

//     pthread_mutex_lock(&ctx->lock);
//     gui_icons_update(ctx->dfb, ARRAY_SIZE(updating_scr_icons), updating_scr_icons);
//     pthread_mutex_unlock(&ctx->lock);

//     return 0;
// }

// static int updating_scr_enter(struct _gui_scr_handle *this)
// {
//     struct win_context *ctx = (struct win_context *)this->ctx;

//     if (NULL == ctx)
//     {
//         GUI_ERR("windows context is NULL");
//         return -1;
//     }

//     /* start timers */
//     pthread_mutex_lock(&ctx->lock);
//     util_timer_start(ctx->clock_timer, TRUE, REFRESH_TIMER_FREQ);
//     pthread_mutex_unlock(&ctx->lock);

//     return 0;
// }

// static int updating_scr_exit(struct _gui_scr_handle *this)
// {
//     struct win_context *ctx = (struct win_context *)this->ctx;

//     if (NULL == ctx)
//     {
//         GUI_ERR("windows context is NULL");
//         return -1;
//     }

//     /* stop timers */
//     pthread_mutex_lock(&ctx->lock);
//     util_timer_stop(ctx->clock_timer);
//     pthread_mutex_unlock(&ctx->lock);
//     util_timer_deinit(ctx->clock_timer);

//     gui_icons_deinit(ARRAY_SIZE(updating_scr_icons), updating_scr_icons);

//     lvgl_clean_obj(ctx->obj);

//     return 0;
// }

// poweroff form screen
static gui_scr_handle_t gui_poweroff_scr_handle = {
    .scr_id = GUI_POWEROFF_SCR_ID,
    .scr_create = poweroff_scr_create,
};
// main form screen
static gui_scr_handle_t gui_main_scr_handle = {
    .scr_id = GUI_MIAN_SCR_ID,
    .scr_create = main_scr_create,
    .scr_enter = main_scr_enter,
    .scr_exit = main_scr_exit,
    .src_message = main_scr_message,
};
// SMS list form screen
static gui_scr_handle_t gui_sms_list_scr_handle = {
    .scr_id = GUI_SMS_LIST_SCR_ID,
    .scr_create = sms_scr_create,
    .scr_enter = sms_scr_enter,
    .scr_exit = sms_scr_exit,
    .src_message = sms_scr_message,
};
// wifi info form screen
static gui_scr_handle_t gui_wifi_info_scr_handle = {
    .scr_id = GUI_WIFI_INFO_SCR_ID,
    .scr_create = wifi_info_scr_create,
    .scr_enter = wifi_info_scr_enter,
    .scr_exit = wifi_info_scr_exit,
};

// QR code form screen
static gui_scr_handle_t gui_qr_code_scr_handle = {
    .scr_id = GUI_QR_CODE_SCR_ID,
    .scr_create = qr_code_scr_create,
    .scr_enter = qr_code_scr_enter,
    .scr_exit = qr_code_scr_exit,
};

// Updating form screen
// static gui_scr_handle_t gui_updating_scr_handle = {
//     .scr_id = GUI_UPDATING_SCR_ID,
//     .scr_create = updating_scr_create,
//     .scr_enter = updating_scr_enter,
//     .scr_exit = updating_scr_exit,
// };

static gui_scr_handle_t *gui_scr_handles[] = {
    &gui_poweroff_scr_handle,
    &gui_main_scr_handle,
    &gui_sms_list_scr_handle,
    &gui_wifi_info_scr_handle,
    &gui_qr_code_scr_handle,
    // &gui_updating_scr_handle,
};

static void scrs_deinit(void)
{
    gui_scr_handle_t *handle = NULL;
    uint32_t i = 0;

    if (NULL == g_reg_scr_list)
    {
        return;
    }

    for (i = 0; i < g_reg_scr_list->cnt; i++)
    {
        handle = g_reg_scr_list->scr_handles[i];

        if (handle && handle->scr_exit)
        {
            handle->scr_exit(handle);
        }
    }

    free(g_reg_scr_list);
    g_reg_scr_list = NULL;

    return;
}

static void scrs_init(struct win_context *ctx)
{
    uint32_t cnt = sizeof(gui_scr_handles) / sizeof(gui_scr_handles[0]);
    uint32_t i;

    GUI_DBG("---Enter---");

    g_reg_scr_list = malloc(sizeof(gui_scr_handle_t) + cnt * sizeof(gui_scr_handle_t *));

    g_reg_scr_list->cnt = cnt;
    for (i = 0; i < cnt; i++)
    {
        gui_scr_handles[i]->ctx = ctx;
        GUI_DBG("---screen id [%u %x]---", gui_scr_handles[i]->scr_id, gui_scr_handles[i]->ctx);
        g_reg_scr_list->scr_handles[i] = (gui_scr_handle_t *)gui_scr_handles[i];
    }
}

static gui_scr_handle_t *gui_scr_find_by_id(uint32_t scr_id)
{
    uint32_t i = 0;

    for (i = 0; i < g_reg_scr_list->cnt; i++)
    {
        if (g_reg_scr_list->scr_handles[i]->scr_id == scr_id)
        {
            return g_reg_scr_list->scr_handles[i];
        }
    }
    return NULL;
}

static bool gui_stack_scr_switch(uint32_t scr_id)
{
    int id = 0;
    struct win_context *ctx = NULL;
    static gui_scr_handle_t *curr_scr = NULL;
    gui_scr_handle_t *tgt_scr = NULL;

    GUI_DBG("---Enter--- [%d]", scr_id);

    id = scr_id;
    if (curr_scr && GUI_POWEROFF_SCR_ID == curr_scr->scr_id)
    {
        GUI_DBG("poweroff screen, do nothing");
        return true;
    }

    if (curr_scr && GUI_UPDATING_SCR_ID == curr_scr->scr_id)
    {
        GUI_DBG("updating screen, do nothing");
        return true;
    }

    if (GUI_ACTIVE_STATE == g_win_state && g_alert_mask)
    {
        if (curr_scr && GUI_ALERT_SCR_ID == curr_scr->scr_id)
        {
            GUI_DBG("alert screen, do nothing");
            return true;
        }

        id = GUI_ALERT_SCR_ID;
    }

    if (curr_scr && curr_scr->scr_id == id)
    {
        GUI_DBG("screen is same, do nothing [%d]", scr_id);
        return true;
    }

    tgt_scr = gui_scr_find_by_id(id);
    if (tgt_scr == NULL)
    {
        GUI_ERR("Can not find target screen [%d]", id);
        return false;
    }

    // free current screen object
    if (curr_scr && curr_scr->scr_exit)
    {
        curr_scr->scr_exit(curr_scr);
    }

    GUI_DBG("---switch screen id [%d %x]---", tgt_scr->scr_id, tgt_scr->ctx);
    if (tgt_scr->scr_create)
    {
        GUI_DBG("target screen create");
        tgt_scr->scr_create(tgt_scr, NULL);
    }

    if (tgt_scr->scr_enter)
    {
        GUI_DBG("target screen enter");
        tgt_scr->scr_enter(tgt_scr);
    }
    curr_scr = tgt_scr;

    ctx = (struct win_context *)curr_scr->ctx;
    ctx->screen_index = id;

    return true;
}

int win_message_event(gui_msg_t *msg)
{
    if(NULL == msg)
    {
        GUI_ERR("input message is NULL");
        return -1;
    }

    if (g_msg_q_id <= 0)
    {
        GUI_ERR("message pipe queue error");
        return -1;
    }
    util_msgsnd(g_msg_q_id, msg, sizeof(gui_msg_t));

    return 0;
}

static int win_poll_init(void *context)
{
    struct win_context *ctx = (struct win_context *)context;
    GUI_DBG("enter win_poll_init");

    ctx->fds = (struct pollfd *)(calloc(MAX_NFDS, sizeof(struct pollfd)));
    if (NULL == ctx->fds)
    {
        GUI_ERR("calloc pollfd error %s", strerror(errno));
        goto ERR;
    }

    ctx->inotify_fd = inotify_init();
    ctx->watch_fd[0] = inotify_add_watch(ctx->inotify_fd, WIN_PATH, IN_CLOSE | IN_DELETE);
    if (ctx->watch_fd[0] == -1)
    {
        GUI_ERR("inotify add watch failed");
        goto ERR;
    }

    ctx->fds[0].fd = ctx->inotify_fd,
    ctx->fds[0].events |= POLLIN;
    ctx->nfds++;

    return 0;

ERR:
    if (ctx->fds)
    {
        free(ctx->fds);
    }

    for (size_t i = 0; i < sizeof(ctx->watch_fd) / sizeof(ctx->watch_fd[0]); i++)
    {
        if (ctx->watch_fd[i] > 0)
        {
            inotify_rm_watch(ctx->inotify_fd, ctx->watch_fd[i]);
        }
    }

    if (ctx->inotify_fd > 0)
    {
        close(ctx->inotify_fd);
    }

    return -1;
}

static int win_poll_post(void *context)
{
    struct win_context *ctx = (struct win_context *)context;

    for (size_t i = 0; i < sizeof(ctx->watch_fd) / sizeof(ctx->watch_fd[0]); i++)
    {
        if (ctx->watch_fd[i] > 0)
        {
            inotify_rm_watch(ctx->inotify_fd, ctx->watch_fd[i]);
        }
    }

    close(ctx->inotify_fd);

    return 0;
}

static int inotify_func_proc(void *context, int fd)
{
    char buf[512] = {0};
    int offset = 0;
    struct inotify_event *event;
    struct win_context *ctx = (struct win_context *)context;

    int nbytes = read(fd, buf, sizeof(buf));
    if (nbytes <= 0)
    {
        return 0;
    }

    do
    {
        event = (struct inotify_event *)(buf + offset);
        GUI_DBG("event len[%d] event name[%s]", event->len, event->name);

        // for (size_t i = 0; i < sizeof(alert_handle) / sizeof(alert_handle[0]); i++)
        // {
        //     if (event->len > 0 && !strcmp(event->name, alert_handle[i].name))
        //     {
        //         if (file_exists(alert_handle[i].path))
        //         {
        //             SET_BIT(g_alert_mask, alert_handle[i].status);
        //         }
        //         else
        //         {
        //             CLEAR_BIT(g_alert_mask, alert_handle[i].status);
        //         }

        //         if (0 != g_alert_mask)
        //         {
        //             gui_stack_scr_switch(GUI_ALERT_SCR_ID);
        //         }
        //         else
        //         {
        //             gui_stack_scr_switch(GUI_MIAN_SCR_ID);
        //         }
        //     }
        }

        offset += sizeof(struct inotify_event) + event->len;
    } while (offset < nbytes);

    return 0;
}

static int win_poll_proc(void *context)
{
    struct win_context *ctx = (struct win_context *)context;
    int ret = 0;
    int idx = 0;

    int timeout = 1 * 1000; // timeout 1s

    ret = poll(ctx->fds, ctx->nfds, timeout);
    if (ret <= 0)
    {
        return 0;
    }

    for (idx = 0; idx < ctx->nfds; idx++)
    {
        if (0 == (ctx->fds[idx].revents & POLLIN))
        {
            continue;
        }

        if (ctx->fds[idx].fd == ctx->inotify_fd)
        {
            inotify_func_proc(ctx, ctx->inotify_fd);
        }
    }

    return 0;
}

static int win_task_proc(void *context)
{
    struct win_context *ctx = (struct win_context *)context;
    gui_msg_t msg_received = {0};
    int bytes_received;

    if(g_msg_q_id <= 0 || NULL == ctx)
    {
        GUI_ERR("g_msg_q_id or windows context is invalid");
        sleep(1);
        return 0;
    }

    bytes_received = util_msgrcv(g_msg_q_id, &msg_received, sizeof(msg_received));
    if (bytes_received == sizeof(msg_received))
    {
        GUI_DBG("Receive Message Type = %u", msg_received.msg_type);
        switch (msg_received.msg_type)
        {
        case BUTTON_EVENT:
            do_button_event(ctx);
            break;
        case USB_EVENT:
            break;
        case IDLE_TIMEOUT_EVENT:
            do_idle_event(ctx);
            break;
        case DISP_IDLE_TIMEOUT_EVENT:
            do_disp_idle_event(ctx);
            break;
        case SYS_SLEEP_EVENT:
            g_win_state = GUI_SLEEP_STATE;
            ctx->gui_state = gui_screen_power(eSCR_POWER_OFF);

            gui_stack_scr_switch(GUI_PROGRESS_SCR_ID);
            break;
        case SYS_WAKEUP_EVENT:
            g_win_state = GUI_WAKEUP_STATE;
            ctx->gui_state = gui_screen_power(eSCR_POWER_ON);
            break;
        case SYS_ACTIVE_EVENT:
            g_win_state = GUI_ACTIVE_STATE;
            gui_stack_scr_switch(GUI_MIAN_SCR_ID);
            /* reset timers */
            GUI_DBG("reset lcd display idle timer [0x%x]", (uint32_t)ctx->disp_idle_timer);
            util_timer_start(ctx->disp_idle_timer, FALSE, IDLE_TIMER_FREQ);
            util_timer_start(ctx->idle_timer, FALSE, ctx->timeout);
            break;
        case SYS_POWEROFF_EVENT:
            GUI_DBG("system poweroff message");
            g_win_state = GUI_POWEROFF_STATE;
            ctx->gui_state = gui_screen_power(eSCR_POWER_ON);

            file_write(POWEROFF_FILE, "");
            gui_stack_scr_switch(GUI_POWEROFF_SCR_ID);
            break;
        default:
            GUI_ERR("Unexpected Message Type = %u", msg_received.msg_type);
            break;
        }
        return 0;
    }

    GUI_ERR("Bad read from message queue. (%d out of %lu bytes)", bytes_received,
            sizeof(msg_received));
    return 0;
}

struct win_context *win_init(void)
{
    struct win_context *ctx = NULL;
    char *p = NULL;

    if (!file_exists(FB_DEV))
    {
        GUI_ERR("no %s device, quit", FB_DEV);
        return NULL;
    }

    ctx = calloc(1, sizeof(*ctx));
    if (!ctx)
    {
        GUI_ERR("calloc ctx failed, quit");
        return NULL;
    }
    pthread_mutex_init(&ctx->lock, NULL);

    ctx->dfb = gui_init();
    if (NULL == ctx->dfb)
    {
        GUI_ERR("gui init failed");
        goto END;
    }

    if (lvgl_init() < 0)
    {
        GUI_ERR("lvgl_init failed, quit");
        goto END;
    }

    ctx->obj = lvgl_create_obj(NULL);
    if (NULL == ctx->obj)
    {
        GUI_ERR("lvgl_create_obj failed");
        goto END;
    }
    GUI_DBG("windows object [0x%x]", ctx->obj);

    g_msg_q_id = util_msgget(GUI_EVENT_PATH, O_RDWR);
    if (g_msg_q_id < 0)
    {
        GUI_ERR("Unable to initialize Message Queue!");
        goto END;
    }

    file_write(POWERON_FILE, "");
    p = nvram_get("trc_web.web.language");
    if (p)
    {
        ctx->language = strcasecmp(p, "en") ? GUI_ICON_LANG_ES : GUI_ICON_LANG_EN;
        set_sys_language(ctx->language);
        free(p);
    }

    ctx->gui_state = gui_screen_power(eSCR_POWER_ON);

    ctx->timeout = 0;
    ctx->language = GUI_ICON_LANG_EN;

    util_timer_init(ctx, &ctx->idle_timer, idle_timer_handler);

    util_timer_init(ctx, &ctx->disp_idle_timer, disp_idle_timer_handler);
    util_timer_start(ctx->disp_idle_timer,  FALSE, IDLE_TIMER_FREQ);

    util_timer_init(ctx, &ctx->param_timer, param_timer_handler);
    /* start timers */
    util_timer_start(ctx->param_timer, TRUE, REFRESH_TIMER_FREQ);
    // windows screen init
    scrs_init(ctx);

    gui_stack_scr_switch(GUI_MIAN_SCR_ID);
    g_win_state = GUI_ACTIVE_STATE;

    util_launch_thelper(&ctx->win_task_helper, /* windows task */
                        NULL,                  /* Initialize func */
                        NULL,                  /* Pre-Process func */
                        win_task_proc,         /* Process func */
                        NULL,                  /* Post-Process func */
                        ctx);

    util_launch_thelper(&ctx->win_poll_helper, /* windows task */
                        win_poll_init,         /* Initialize func */
                        NULL,                  /* Pre-Process func */
                        win_poll_proc,         /* Process func */
                        win_poll_post,         /* Post-Process func */
                        ctx);

    GUI_DBG("win_task_init finished");

END:
    return ctx;
}

int win_deinit(struct win_context *ctx)
{
    if (NULL == ctx)
    {
        return 0;
    }

    util_unblock_thelper(&ctx->win_task_helper);
    util_unblock_thelper(&ctx->win_poll_helper);
    util_timer_stop(ctx->idle_timer);
    util_timer_stop(ctx->disp_idle_timer);
    util_timer_stop(ctx->param_timer);

    util_timer_deinit(ctx->idle_timer);
    util_timer_deinit(ctx->disp_idle_timer);
    util_timer_deinit(ctx->param_timer);

    gui_deinit(ctx->dfb);
    lvgl_del_obj(ctx->obj);

    lvgl_deinit();
    scrs_deinit();

    util_join_thelper(&ctx->win_task_helper);
    util_join_thelper(&ctx->win_poll_helper);

    free(ctx);

    return 0;
}
