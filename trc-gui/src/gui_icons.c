/***********************************************************************************************
 *
 *    Filename: gui_icons.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Fri 10 May 2024 10:25:59 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>

#include "gui_icons.h"
#include "gui_comm.h"
#include "gui_util.h"

static int g_lang = GUI_ICON_LANG_EN;
static int g_main_scr_mask = 0;

enum e_string_id
{
    eWIFI_NAME = 0,
    eWIFI_KEY,
    eNO_SIM,
    eINVALID_SIM,
    eSIM_PIN,
    eSIM_PUK,
    eNO_SEVICE,
    eLOW_BATT,
    eBATT_FULL,
};

typedef struct icon_string_t
{
    const char *str[GUI_LANG_MAX];
} icon_str;

static icon_str g_icon_str[] = {{"Wi-Fi Name:", ""},
                                {"Password:", ""},
                                {"No SIM Found.", ""},
                                {"Invalid SIM", ""},
                                {"SIM PIN Locked", ""},
                                {"SIM PUK Locked", ""},
                                {"No Service Available", ""},
                                {"Low battery", ""},
                                {"Battery fully charged", ""},};

char g_alert_path[MAX_ALERT_PATH][256] = {0};

/******************************************************************************
 * Image Icons
 ******************************************************************************/

/*----------------------------------------------------------------------------
 * Battery
 *---------------------------------------------------------------------------*/
static const img_array_item battery_icon_arr[] = {
    { .key = 0, .path = "f"IMAGEDIR"/battery_no.bin", },
    { .key = 1, .path = "f"IMAGEDIR"/battery_5.bin", },
    { .key = 2, .path = "f"IMAGEDIR"/battery_25.bin", },
    { .key = 3, .path = "f"IMAGEDIR"/battery_50.bin", },
    { .key = 4, .path = "f"IMAGEDIR"/battery_75.bin", },
    { .key = 5, .path = "f"IMAGEDIR"/battery_full.bin", },
    { .key = 6, .path = "f"IMAGEDIR"/battery_charging.bin", },
};

static int battery_val2key(struct gui_icon *this, void *data)
{
    static int idx = 0;
    int value = 0;

    //value = tmpnvram_getint("dmtmp.pwr.type");
    value = tmpnvram_getint("dmtmp.batt.status");
    switch(value)
    {
    case 1:// not charging
        break;
    case 2:// charge full
        unlink(BATT_LOW_FILE);
        return 5;
        break;
    case 3:// charging
        unlink(BATT_LOW_FILE);
        return 6;
        break;
    }

    if (file_exists(BATT_LOW_FILE))
    {
        return idx++ % 2 ? 0 : -1;
    }

    value = tmpnvram_getint("dmtmp.batt.level");
    //GUI_DBG("---batt level [%d]---", value);
    if (value >=0 && value < 6)
    {
        return value;
    }

    return this->image.default_key;
}

static struct gui_icon battery_icon = {
    .name = GUI_BATTERY_ICON,
    .type = GUI_ICON_TYPE_IMAGE,
    .image = {
        .arr = battery_icon_arr,
        .arr_size = ARRAY_SIZE(battery_icon_arr),
        .val_to_key = battery_val2key,
        .default_key = 0, /* unknown battery status */
        .rect = {
            .x = 109,
            .y = 4,
            .w = 18,
            .h = 11,
        }
    },
};

/*----------------------------------------------------------------------------
 * Cellular
 *---------------------------------------------------------------------------*/
static const img_array_item signal_icon_arr[] = {
    { .key = 0, .path = "f"IMAGEDIR"/none.bin", },
    { .key = 1, .path = "f"IMAGEDIR"/no_sim.bin", },
    { .key = 2, .path = "f"IMAGEDIR"/sim_error.bin", },
    { .key = 3, .path = "f"IMAGEDIR"/sim_lock.bin", },
    { .key = 4, .path = "f"IMAGEDIR"/signal_0.bin", },
    { .key = 5, .path = "f"IMAGEDIR"/signal_1.bin", },
    { .key = 6, .path = "f"IMAGEDIR"/signal_2.bin", },
    { .key = 7, .path = "f"IMAGEDIR"/signal_3.bin", },
    { .key = 8, .path = "f"IMAGEDIR"/signal_4.bin", },
    { .key = 9, .path = "f"IMAGEDIR"/signal_5.bin", },
};

static int signal_val2key(struct gui_icon *this, void *data)
{
    int value = 0;

    value = tmpnvram_getint("mmtmp.netstatus.sim");
    //GUI_DBG("---sim status [%d]---", value);
    switch (value)
    {
    case 0: // no sim
        return 1;
        break;
    case 2: // sim error
        return 2;
        break;
    case 3:
    case 4:
    case 5: // sim lock
        return 3;
        break;
    default: // sim ready
        break;
    }

    if (2 != tmpnvram_getint("mmtmp.netstatus.reg") || tmpnvram_getint("mmtmp.netstatus.sig") <= 0)
    {
        return 4;
    }

    return tmpnvram_getint("mmtmp.netstatus.sig") + 4;
}

static struct gui_icon signal_icon = {
    .name = GUI_SIGNAL_ICON,
    .type = GUI_ICON_TYPE_IMAGE,
    .image = {
        .arr = signal_icon_arr,
        .arr_size = ARRAY_SIZE(signal_icon_arr),
        .val_to_key = signal_val2key,
        .default_key = 0, /* cellular off */
        .rect = {
            .x = 1,
            .y = 4,
            .w = 14,
            .h = 11,
        },
    },
};

/*----------------------------------------------------------------------------
 * Roaming
 *---------------------------------------------------------------------------*/
#define NET_TYPE_3G   2
#define NET_TYPE_4G   3
#define NET_TYPE_5G   4
static const img_array_item net_type_icon_arr[] = {
    {.key = 0, .path = "f"IMAGEDIR"/none.bin",},
    {.key = 1, .path = "f"IMAGEDIR"/roaming.bin",},
    {.key = 2, .path = "f"IMAGEDIR"/3g.bin",},
    {.key = 3, .path = "f"IMAGEDIR"/4g.bin",},
    {.key = 4, .path = "f"IMAGEDIR"/5g.bin",},
};

static int net_type_val2key(struct gui_icon *this, void *data)
{
    static int old_roam = 0;
    int value = 0;

    if (2 != tmpnvram_getint("mmtmp.netstatus.reg"))
    {
        return 0;
    }

    value = tmpnvram_getint("mmtmp.netstatus.roam");
    if (1 == nvram_getint("mm.roam_display.flag") || old_roam != value)
    {
        old_roam = value;
        if (1 == value)
        {
            file_write(ROAM_FILE, "");
        }
        else
        {
            unlink(ROAM_FILE);
        }
        nvram_set("mm.roam_display.flag", "0");
    }

    if (1 == old_roam)
    {
        return 1;
    }

    value = tmpnvram_getint("mmtmp.netstatus.net_mode");
    if (NET_TYPE_3G == value)
    {
        return 2;
    }
    else if (NET_TYPE_4G == value)
    {
        return 3;
    }
    else if (NET_TYPE_5G == value)
    {
        return 4;
    }

    return this->image.default_key;
}

static struct gui_icon net_type_icon = {
    .name = GUI_NET_TYPE_ICON,
    .type = GUI_ICON_TYPE_IMAGE,
    .image = {
        .arr = net_type_icon_arr,
        .arr_size = ARRAY_SIZE(net_type_icon_arr),
        .val_to_key = net_type_val2key,
        .default_key = 0,
        .rect = {
            .x = 18,
            .y = 5,
            .w = 13,
            .h = 11,
        },
    },
};


/******************************************************************************
 * Text Icons
 ******************************************************************************/

/*----------------------------------------------------------------------------
 * WiFi SSID
 *---------------------------------------------------------------------------*/
static char *wifiname_val2string(struct gui_icon *this, void *data)
{
    return strdup(g_icon_str[eWIFI_NAME].str[g_lang]);
}

static struct gui_icon wifiname_icon = {
    .name = GUI_WIFI_NAME_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "",
        .val_to_string = wifiname_val2string,
        .pos = {
            .x = 3,
            .y = 3,
            .align = TA_LEFT,
        },
    },
};

static char *wifissid_val2string(struct gui_icon *this, void *data)
{
    char value[256] = {0};
    char *pssid = NULL;
    char *pfact_ssid = NULL;

    snprintf(value, sizeof(value), "");
    if (0 == nvram_getint("wireless.wifi0.disabled"))
    {
        // main 2.4G
        pssid = nvram_get("wireless.@wifi-iface[0].ssid");
        pfact_ssid = factnvram_get("wlan_ap0_ssid");

        if (NULL != pssid && strlen(pssid) > 0)
        {
            snprintf(value, sizeof(value), "%s", pssid);
        }
        else if (NULL != pfact_ssid && strlen(pfact_ssid) > 0)
        {
            snprintf(value, sizeof(value), "%s", pfact_ssid);
        }

        if (pssid)
        {
            free(pssid);
        }
        if (pfact_ssid)
        {
            free(pfact_ssid);
        }

        return strdup(value);
    }

    if (0 == nvram_getint("wireless.wifi2.disabled"))
    {
        // main 5G
        pssid = nvram_get("wireless.@wifi-iface[2].ssid");
        if (NULL != pssid && strlen(pssid) > 0)
        {
            snprintf(value, sizeof(value), "%s", pssid);
        }
        if (pssid)
        {
            free(pssid);
        }
        return strdup(value);
    }

    return strdup(value);
}

static struct gui_icon wifissid_icon = {
    .name = GUI_WIFI_SSID_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "T10 XXX",
        .val_to_string = wifissid_val2string,
        .pos = {
            .x = 3,
            .y = 20,
            .align = TA_LEFT,
        },
    },
};

/*----------------------------------------------------------------------------
 * WiFi Password
 *---------------------------------------------------------------------------*/
static char *wifipasswd_val2string(struct gui_icon *this, void *data)
{
    return strdup(g_icon_str[eWIFI_KEY].str[g_lang]);
}
static struct gui_icon wifipasswd_icon = {
    .name = GUI_WIFI_PASSWD_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "",
        .val_to_string = wifipasswd_val2string,
        .pos = {
            .x = 3,
            .y = 3,
            .align = TA_LEFT,
        },
    },
};

static char *wifikey_val2string(struct gui_icon *this, void *data)
{
    char value[256] = {0};
    char *pkey = NULL;
    char *pfact_key = NULL;
    char *p = NULL;

    snprintf(value, sizeof(value), "");
    if (0 == nvram_getint("wireless.wifi0.disabled"))
    {
        // main 2.4G
        pkey = nvram_get("wireless.@wifi-iface[0].key");
        pfact_key = factnvram_get("wlan_ap0_password");

        if (NULL != pkey && strlen(pkey) > 0)
        {
            snprintf(value, sizeof(value), "%s", pkey);
        }
        else if (NULL != pfact_key && strlen(pfact_key) > 0)
        {
            snprintf(value, sizeof(value), "%s", pfact_key);
        }

        if (pkey)
        {
            free(pkey);
        }
        if (pfact_key)
        {
            free(pfact_key);
        }
        return strdup(value);
    }

    if (0 == nvram_getint("wireless.wifi2.disabled"))
    {
        // main 5G
        pkey = nvram_get("wireless.@wifi-iface[2].key");
        if (NULL != pkey && strlen(pkey) > 0)
        {
            snprintf(value, sizeof(value), "%s", pkey);
        }
        if (pkey)
        {
            free(pkey);
        }
        return strdup(value);
    }

    return strdup(value);
}

static struct gui_icon wifikey_icon = {
    .name = GUI_WIFI_KEY_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "XXXXXX",
        .val_to_string = wifikey_val2string,
        .pos = {
            .x = 3,
            .y = 20,
            .align = TA_LEFT,
        },
    },
};

/*----------------------------------------------------------------------------
 * Main Error Message Information
 *---------------------------------------------------------------------------*/
static char *main_error_val2string(struct gui_icon *this, void *data)
{
    int value = 0;

    value = tmpnvram_getint("mmtmp.netstatus.sim");
    switch (value)
    {
    case 0: // no sim
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eNO_SIM].str[g_lang]);
        break;
    case 2: // sim error
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eINVALID_SIM].str[g_lang]);
        break;
    case 3:
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eSIM_PIN].str[g_lang]);
        break;
    case 4:
    case 5: // sim lock
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eSIM_PUK].str[g_lang]);
        break;
    default: // sim ready
        break;
    }

    if (2 != tmpnvram_getint("mmtmp.netstatus.reg") || tmpnvram_getint("mmtmp.netstatus.sig") <= 0)
    {
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eNO_SEVICE].str[g_lang]);
    }

    if (file_exists(BATT_LOW_FILE))
    {
        g_main_scr_mask |= MAIN_ERROR_ICON;
        return strdup(g_icon_str[eLOW_BATT].str[g_lang]);
    }

    g_main_scr_mask &= ~MAIN_ERROR_ICON;
    return strdup("");
}

static struct gui_icon main_error_icon = {
    .name = GUI_MAIN_ERROR_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "",
        .val_to_string = main_error_val2string,
        .pos = {
            .x = 0,
            .y = 20,
            .align = TA_LINE_CENTER,
        },
    },
};

/*----------------------------------------------------------------------------
 * Main Alert Message Information
 *---------------------------------------------------------------------------*/
static char *main_alert_val2string(struct gui_icon *this, void *data)
{
    if (g_main_scr_mask & MAIN_ERROR_ICON)
    {
        return strdup("");
    }

    g_main_scr_mask &= ~MAIN_ALERT_ICON;
    return strdup("");
}

static struct gui_icon main_alert_icon = {
    .name = GUI_MAIN_ALERT_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "",
        .val_to_string = main_alert_val2string,
        .pos = {
            .x = 0,
            .y = 20,
            .align = TA_LINE_CENTER,
        },
    },
};

/*----------------------------------------------------------------------------
 * Main Notice Message Information
 *---------------------------------------------------------------------------*/
static char *main_notice_val2string(struct gui_icon *this, void *data)
{

    if (g_main_scr_mask & MAIN_ERROR_ICON || g_main_scr_mask & MAIN_ALERT_ICON)
    {
        return strdup("");
    }

    g_main_scr_mask |= MAIN_NOTICE_ICON;
    return strdup("");
}

static struct gui_icon main_notice_icon = {
    .name = GUI_MAIN_NOTICE_ICON,
    .type = GUI_ICON_TYPE_TEXT,
    .text = {
        .default_string = "",
        .val_to_string = main_notice_val2string,
        .pos = {
            .x = 0,
            .y = 20,
            .align = TA_LINE_CENTER,
        },
    },
};

/* icons database (order matters - see e_gui_icons) */
struct gui_icon *gui_icons[] = {
    &signal_icon,
    &battery_icon,
    &net_type_icon,
    &wifiname_icon,
    &wifissid_icon,
    &wifipasswd_icon,
    &wifikey_icon,
    &main_error_icon,
    &main_alert_icon,
    &main_notice_icon,
};

size_t gui_num_icons = ARRAY_SIZE(gui_icons);

int set_sys_language(int lang)
{
    g_lang = lang;

    return 0;
}

int get_msg_form(void)
{
    return g_main_scr_mask;
}
