/***********************************************************************************************
 *
 *    Filename: gui_icons.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Fri 10 May 2024 10:25:29 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __GUI_ICONS_H__
#define __GUI_ICONS_H__

#include <stdio.h>

#include "gui_comm.h"
#include "lvgl_object.h"
#include "lvgl_api.h"

#define GUI_BATTERY_ICON      "battery"
#define GUI_SIGNAL_ICON       "signal"
#define GUI_SMS_ICON          "sms"
#define GUI_WIFI_USER_ICON    "wifi user"
#define GUI_TRAFFIC_CION      "traffic"
#define GUI_NET_TYPE_ICON     "network"
#define GUI_LOGO_ICON         "logo information"
#define GUI_INFOR_ICON        "information"
#define GUI_WIFI_NAME_ICON    "wifi name"
#define GUI_WIFI_SSID_ICON    "wifi ssid"
#define GUI_WIFI_PASSWD_ICON  "wifi password"
#define GUI_WIFI_KEY_ICON     "wifi Key"
#define GUI_GUEST_NAME_ICON   "guest name"
#define GUI_GUEST_SSID_ICON   "guest ssid"
#define GUI_GUEST_PASSWD_ICON "guest passwd"
#define GUI_GUEST_KEY_ICON    "guest key"
#define GUI_NOTICE_ICON       "notice infor"
#define GUI_OUT_SEV_MSG_ICON  "out service msg"
#define GUI_TRAFFIC_BAR_ICON  "traffic bar"
#define GUI_TRAFFIC_DATE_ICON "traffic date"
#define GUI_TRAFFIC_DATA_ICON "traffic data"
#define GUI_PROGRESS_ICON     "progress icon"
#define GUI_DATA_ACCESS_ICON  "data access icon"
#define GUI_MAIN_ERROR_ICON   "main error message"
#define GUI_MAIN_ALERT_ICON   "main alert message"
#define GUI_MAIN_NOTICE_ICON  "main notice message"
#define GUI_DEVICE_ALERT_ICON "device alert icon"
#define GUI_QR_CODE_ICON      "qr code icon"
#define GUI_UPDATING_ICON     "updating icon"

#define MAIN_ERROR_ICON       0x01
#define MAIN_ALERT_ICON       0x02
#define MAIN_NOTICE_ICON      0x04

enum e_icon_type
{
    GUI_ICON_TYPE_IMAGE = 0,
    GUI_ICON_TYPE_TEXT,
    GUI_ICON_TYPE_BAR,
    GUI_ICON_TYPE_END,
};

enum e_icon_language
{
    GUI_ICON_LANG_EN = 0,
    GUI_ICON_LANG_ES,
    GUI_LANG_MAX
};

struct gui_icon
{
    const char *name;
    int id;
    enum e_icon_type type;
    void *h;

    /* information for image icons */
    struct
    {
        const img_array_item *arr;
        const size_t arr_size;
        int (*val_to_key)(struct gui_icon *this, void *data);
        int default_key; /* default image key */
        int cur_key;
        object_rect rect;
    } image;

    /* information for label text */
    struct
    {
        const char *font_path;
        const char *default_string;
        char *(*val_to_string)(struct gui_icon *this, void *data);
        int rgb;
        object_pos pos;
    } text;

    /* information for bar widget */
    struct
    {
        int (*val_to_bar)(struct gui_icon *this, void *data);
        int default_value; /* default bar value */
        int cur_value;
        object_rect rect;
    } bar;
};

int set_sys_language(int lang);
int get_msg_form(void);

#endif /* __GUI_ICONS_H__ */
