#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>

#include <libubus.h>
#include <libubox/blobmsg_json.h>
#include "cJSON.h"

#include "ubus_helper.h"
#include "gui_comm.h" // For GUI_DBG, GUI_ERR

static struct ubus_context *g_ubus_ctx = NULL;
static gui_ubus_event_cb_t g_event_handler = NULL;

static int gui_ubus_notify(struct ubus_context *ctx, struct ubus_object *obj,
                           struct ubus_request_data *req, const char *method,
                           struct blob_attr *msg);

enum {
    NOTIFY_PAYLOAD,
    __NOTIFY_MAX
};

static const struct blobmsg_policy notify_policy[__NOTIFY_MAX] = {
    [NOTIFY_PAYLOAD] = { .name = "payload", .type = BLOBMSG_TYPE_TABLE },
};

static struct ubus_method gui_methods[] = {
    UBUS_METHOD("notify", gui_ubus_notify, notify_policy),
};

static struct ubus_object_type gui_object_type =
    UBUS_OBJECT_TYPE("gui", gui_methods);

static struct ubus_object gui_object = {
    .name = "gui",
    .type = &gui_object_type,
    .methods = gui_methods,
    .n_methods = ARRAY_SIZE(gui_methods),
};

static int gui_ubus_notify(struct ubus_context *ctx, struct ubus_object *obj,
                           struct ubus_request_data *req, const char *method,
                           struct blob_attr *msg)
{
    char *json_str = NULL;
    cJSON *payload_json = NULL;
    cJSON *event_json = NULL;
    cJSON *data_json = NULL;
    const char *event_str = NULL;

    if (!msg) {
        return UBUS_STATUS_INVALID_ARGUMENT;
    }

    json_str = blobmsg_format_json(msg, true);
    if (!json_str) {
        GUI_ERR("Failed to format blobmsg to json");
        return UBUS_STATUS_UNKNOWN_ERROR;
    }

    payload_json = cJSON_Parse(json_str);
    free(json_str);
    if (!payload_json) {
        GUI_ERR("Failed to parse json payload");
        return UBUS_STATUS_INVALID_ARGUMENT;
    }

    event_json = cJSON_GetObjectItem(payload_json, "event");
    if (!cJSON_IsString(event_json)) {
        GUI_ERR("Missing or invalid 'event' field in payload");
        cJSON_Delete(payload_json);
        return UBUS_STATUS_INVALID_ARGUMENT;
    }
    event_str = event_json->valuestring;

    data_json = cJSON_GetObjectItem(payload_json, "data");

    if (g_event_handler) {
        g_event_handler(event_str, data_json);
    }

    cJSON_Delete(payload_json);
    return UBUS_STATUS_OK;
}

int ubus_helper_init(gui_ubus_event_cb_t event_handler)
{
    if (g_ubus_ctx) {
        GUI_DBG("ubus helper already initialized.");
        return 0;
    }

    g_ubus_ctx = ubus_connect(NULL);
    if (!g_ubus_ctx) {
        GUI_ERR("Failed to connect to ubus");
        return -1;
    }

    g_event_handler = event_handler;
    ubus_add_uloop(g_ubus_ctx);
    
    if (ubus_add_object(g_ubus_ctx, &gui_object) != 0) {
        GUI_ERR("Failed to add ubus object");
        ubus_free(g_ubus_ctx);
        g_ubus_ctx = NULL;
        return -1;
    }

    GUI_DBG("Connected to ubus and registered 'gui' object");
    return 0;
}

void ubus_helper_deinit(void)
{
    struct ubus_subscription *sub = g_subscriptions;
    struct ubus_subscription *next_sub;

    while (sub) {
        next_sub = sub->next;
        ubus_unregister_event_handler(g_ubus_ctx, &sub->ev_handler);
        free(sub->event_pattern);
        free(sub);
        sub = next_sub;
    }
    g_subscriptions = NULL;

    if (g_ubus_ctx) {
        ubus_free(g_ubus_ctx);
        g_ubus_ctx = NULL;
    }
}

// Struct to hold the data for the callback
struct ubus_request_data {
    cJSON *json_result;
    int error;
};

// Callback for ubus_invoke
static void ubus_invoke_callback(struct ubus_request *req, int type, struct blob_attr *msg)
{
    struct ubus_request_data *req_data = (struct ubus_request_data *)req->priv;
    char *json_str = NULL;

    if (!msg) {
        req_data->error = 1;
        return;
    }

    json_str = blobmsg_format_json(msg, true);
    if (!json_str) {
        GUI_ERR("Failed to format blobmsg to json");
        req_data->error = 1;
        return;
    }

    req_data->json_result = cJSON_Parse(json_str);
    free(json_str);

    if (!req_data->json_result) {
        GUI_ERR("Failed to parse json result");
        req_data->error = 1;
    }
}

cJSON* ubus_call_sync(const char *path, const char *method, const char *params_json)
{
    uint32_t id;
    int ret;
    struct blob_buf b = {};
    struct ubus_request_data req_data = { .json_result = NULL, .error = 0 };

    if (!g_ubus_ctx) {
        GUI_ERR("ubus context is not initialized");
        return NULL;
    }

    blob_buf_init(&b, 0);

    // Convert JSON params to blob message
    if (params_json) {
        if (!blobmsg_add_json_from_string(&b, params_json)) {
            GUI_ERR("Failed to convert json params to blobmsg: %s", params_json);
            blob_buf_free(&b);
            return NULL;
        }
    }

    if (ubus_lookup_id(g_ubus_ctx, path, &id) != 0) {
        GUI_ERR("Failed to lookup object: %s", path);
        blob_buf_free(&b);
        return NULL;
    }

    // Use a 5-second timeout for the synchronous call
    ret = ubus_invoke(g_ubus_ctx, id, method, b.head, ubus_invoke_callback, &req_data, 5 * 1000);
    blob_buf_free(&b);

    if (ret != 0) {
        GUI_ERR("ubus_invoke failed for %s.%s with error: %s", path, method, ubus_strerror(ret));
        return NULL;
    }

    if (req_data.error) {
        GUI_ERR("Error in ubus_invoke_callback");
        if(req_data.json_result) {
            cJSON_Delete(req_data.json_result);
        }
        return NULL;
    }

    return req_data.json_result;
}

int ubus_helper_get_fd(void)
{
    if (!g_ubus_ctx) {
        return -1;
    }
    return g_ubus_ctx->sock.fd;
}

void ubus_helper_handle_events(void)
{
    if (g_ubus_ctx) {
        ubus_handle_event(g_ubus_ctx);
    }
} 