AM_CFLAGS = -Wundef \
        -Wstrict-prototypes \
        -Wno-trigraphs \
        -g -O0 \
        -fno-inline \
        -fno-short-enums \
        -fpic \
        -fpie -pie \
        -I. \
        $(LVGL_CFLAGS) \
        -DFONTDIR=\"${resdir}/fonts\" \
        -DIMAGEDIR=\"${resdir}/images\"

ACLOCAL_AMFLAGS = -I m4

requiredlibs = $(LVGL_LIBS)

bin_PROGRAMS = trc_gui

fontsdir = $(datadir)/trcgui/fonts/
fonts_DATA = $(wildcard data/fonts/*)

imagesdir = $(datadir)/trcgui/images/
images_DATA = $(wildcard data/images/*)

resdir = ${datadir}/trcgui

init_ddir = ${sysconfdir}/init.d
init_d_SCRIPTS = trc_gui.init

#============================= trc_gui =============================
trc_gui_c_sources = gui_daemon.c sys_manager.c gui_util.c event_manager.c gui_icons.c win_manager.c cJSON.c ubus_helper.c sms_data.c

trc_gui_CC = @CC@

trc_gui_SOURCES = $(trc_gui_c_sources)
trc_gui_CFLAGS = $(AM_CFLAGS)
trc_gui_LDFLAGS = -lpthread -lrt
trc_gui_LDADD = $(requiredlibs)
#=================================================================
