/***********************************************************************************************
 *
 *    Filename: win_manager.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:19:00 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __WIN_MANAGER_H__
#define __WIN_MANAGER_H__

#include <stdio.h>
#include <stdbool.h>

#include "gui_util.h"

typedef struct _gui_scr_handle
{
    uint32_t scr_id;
    void *ctx; // window context
    int (*scr_create)(struct _gui_scr_handle *this, void *parent);
    int (*scr_enter)(struct _gui_scr_handle *this);
    int (*scr_exit)(struct _gui_scr_handle *this);
    int (*src_message)(struct _gui_scr_handle *this, const char *msg);
} gui_scr_handle_t;

typedef struct _gui_scr_handle_list
{
    uint32_t cnt;
    gui_scr_handle_t *scr_handles[0];
} gui_scr_handle_list_t;

struct win_context
{
    struct util_thelper win_task_helper;
    struct util_thelper win_poll_helper;
    pthread_mutex_t lock;
    timer_t idle_timer;
    timer_t disp_idle_timer;
    timer_t param_timer;
    timer_t clock_timer;
    int screen_index;
    int sms_current_page; // ADD: page index for SMS list screen
    int gui_state;
    int timeout;

    int language;

    void *dfb;
    void *obj;

    struct pollfd *fds;
    int nfds;
    int inotify_fd;
    int watch_fd[WATCH_MAXFD];
};

struct win_context *win_init(void);
int win_deinit(struct win_context *ctx);
int win_message_event(gui_msg_t *msg);

#endif /* __WIN_MANAGER_H__ */

