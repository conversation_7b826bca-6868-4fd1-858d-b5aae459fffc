/***********************************************************************************************
 *
 *    Filename: sms_data.h
 *
 * Description: Header for SMS data access layer.
 *
 **********************************************************************************************/

#ifndef __SMS_DATA_H__
#define __SMS_DATA_H__

#define MAX_PHONE_NUMBER_LEN 16

typedef struct {
    char phone_number[MAX_PHONE_NUMBER_LEN];
} sms_message_t;

/**
 * @brief Get the total count of unread SMS messages.
 *
 * @return The total number of messages.
 */
int sms_get_total_count(void);

/**
 * @brief Get messages for a specific page.
 *
 * @param page_index The zero-based index of the page to retrieve.
 * @param out_messages An array to be filled with the message data.
 * @param items_per_page The number of items that constitute a full page.
 * @return The number of messages actually retrieved for the page.
 */
int sms_get_messages_for_page(int page_index, sms_message_t out_messages[], int items_per_page);

#endif // __SMS_DATA_H__