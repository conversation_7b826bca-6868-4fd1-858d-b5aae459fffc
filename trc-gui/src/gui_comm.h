/***********************************************************************************************
 *
 *    Filename: gui_comm.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:21:52 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __GUI_COMM_H__
#define __GUI_COMM_H__

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>

#if 0
#define GUI_DBG(formats, args...) \
    log_print(LC_LOG_DEBUG, "[DBG][%s:<%s>%d]"formats"\n", __FILE__,__FUNCTION__, __LINE__, ##args);
#define GUI_ERR(formats, args...) \
    log_print(LC_LOG_ERROR, "[ERR][%s:<%s>%d]"formats"\n", __FILE__,__FUNCTION__, __LINE__, ##args);
#define GUI_INF(formats, args...) \
    log_print(LC_LOG_INFO, "[INF][%s:<%s>%d]"formats"\n", __FILE__, __FUNCTION__,__LINE__, ##args);
#else
#define GUI_DBG(formats, args...) \
    printf("[DBG][%s:%d]"formats"\n", __FILE__, __LINE__, ##args);
#define GUI_ERR(formats, args...) \
    printf("[ERR][%s:%d]"formats"\n", __FILE__, __LINE__, ##args);
#define GUI_INF(formats, args...) \
    printf("[INF][%s:%d]"formats"\n", __FILE__, __LINE__, ##args);
#endif

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(_A) (sizeof(_A) / sizeof((_A)[0]))
#endif /* ARRAY_SIZE */

#ifndef MIN
#define MIN(x, y) ((x) < (y) ? (x) : (y))
#endif

#ifndef MAX
#define MAX(x, y) ((x) > (y) ? (x) : (y))
#endif

#define SET_BIT(reg, bit)        ((reg) |= (1 << (bit)))
#define CLEAR_BIT(reg, bit)      ((reg) &= ~(1 << (bit)))

#define TRUE                     (1)
#define FALSE                    (0)

#define UNUSED(x)                ((void)(x))

#define WATCH_MAXFD              (5)
#define MAX_NFDS                 (50)
#define WIN_PATH                 "/tmp/win/"
#define MAX_ALERT_PATH           (3)

#define POWEROFF_NAME            ".poweroff"
#define POWEROFF_FILE            WIN_PATH POWEROFF_NAME

#define POWERON_NAME             ".poweron"
#define POWERON_FILE             WIN_PATH POWERON_NAME
#define BATT_LOW_FILE            WIN_PATH ".batt_low"
#define BATT_FULL_FILE           WIN_PATH ".batt_full"
#define ROAM_FILE                WIN_PATH ".roam"
#define DATALIMIT_FILE           WIN_PATH ".data_limit"
#define CLIENT_FILE              "/tmp/.client_online_all"

#define NEWSMS_NAME              ".new_sms"
#define NEWSMS_FILE              WIN_PATH NEWSMS_NAME

#define ROAMALERT_NAME           ".roam_alert"
#define ROAMALERT_FILE           WIN_PATH ROAMALERT_NAME

#define FACTRESET_NAME           ".fact_reset"
#define FACTRESET_FILE           WIN_PATH FACTRESET_NAME

#define BATT_EXIST_NAME          "win_batt_exit"
#define BATT_EXIST_FILE          WIN_PATH BATT_EXIST_NAME

#define USB_CONN_NAME            "usb_conn"
#define USB_CONN_FILE            WIN_PATH USB_CONN_NAME

#define USB_CONN48_HOUR_NAME     "usb_conn48_hour"
#define USB_CONN48_HOUR_FILE     WIN_PATH USB_CONN48_HOUR_NAME

#define USB_CONN120_HOUR_NAME    "usb_conn120_hour"
#define USB_CONN120_HOUR_FILE    WIN_PATH USB_CONN120_HOUR_NAME

#define DEV_TEMP_HIGH_NAME       "device_temp_high"
#define DEV_TEMP_HIGH_FILE       WIN_PATH DEV_TEMP_HIGH_NAME

#define DEV_TEMP_HIGH_CRIT_NAME  "device_temp_high_critical"
#define DEV_TEMP_HIGH_CRIT_FILE  WIN_PATH DEV_TEMP_HIGH_CRIT_NAME

#define BATT_TEMP_HIGH_NAME      "batt_temp_high"
#define BATT_TEMP_HIGH_FILE      WIN_PATH BATT_TEMP_HIGH_NAME

#define BATT_TEMP_LOW_NAME       "batt_temp_low"
#define BATT_TEMP_LOW_FILE       WIN_PATH BATT_TEMP_LOW_NAME

#define BATT_TEMP_LOW_CRIT_NAME  "batt_temp_low_critical"
#define BATT_TEMP_LOW_CRIT_FILE  WIN_PATH BATT_TEMP_LOW_CRIT_NAME

#define BATT_TEMP_HIGH_CRIT_NAME "batt_temp_high_critical"
#define BATT_TEMP_HIGH_CRIT_FILE WIN_PATH BATT_TEMP_HIGH_CRIT_NAME

enum
{
    GUI_INIT_STATE = 0,
    GUI_SLEEP_STATE,
    GUI_WAKEUP_STATE,
    GUI_ACTIVE_STATE,
    GUI_POWEROFF_STATE,
    GUI_RESET_STATE,
    GUI_STATE_MAX,
};

typedef int32_t gui_retcode_t;

typedef enum error_code
{
    RET_ERROR = -1,
    RET_OK = 0,
    RET_ERR_GENERAL_FAILURE,
    RET_ERR_UNSUPPORTED,
    RET_ERR_INVALID_PARAMETER,
    RET_ERR_ENGINE_BUSY,
    RET_ERR_PHONE_OFFLINE,
    RET_ERR_TIMEOUT,
    RET_ERR_SERVICE_NOT_PRESENT,
    RET_ERR_SERVICE_VERSION_UNSUPPORTED,
    RET_ERR_CLIENT_VERSION_UNSUPPORTED,
    RET_ERR_INVALID_HANDLE,
    RET_ERR_INTERNAL,
    RET_ERR_NOT_INITIALIZED,
    RET_ERR_NOT_ENOUGH_MEMORY,
    RET_INVALID_PARAM,
    RET_NOT_INIT,
    RET_INTERNAL,
    RET_OPEN_FAIL,
    RET_READ_FAIL,
    RET_WRITE_FAIL,
    RET_OPT_FAIL,
    RET_CMAC_ERR,
    RET_CMC_OPEN_ERR,
    RET_BND,
} error_code_t;

typedef enum
{
    GUI_MQ_EVT,
} mq_msg_id_t;

typedef struct _mq_msg_t
{
    uint8_t *pmsg;
    uint32_t len;
} mq_msg_t;

#endif /* __GUI_COMM_H__ */

