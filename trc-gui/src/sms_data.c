/***********************************************************************************************
 *
 *    Filename: sms_data.c
 *
 * Description: Placeholder implementation for SMS data access layer.
 *
 **********************************************************************************************/

#include <stdio.h>
#include <string.h>
#include "sms_data.h"

#define MAX_SMS_COUNT 10

/**
 * @brief Get the total count of unread SMS messages (placeholder).
 */
int sms_get_total_count(void)
{
    return MAX_SMS_COUNT;
}

/**
 * @brief Get messages for a specific page (placeholder).
 */
int sms_get_messages_for_page(int page_index, sms_message_t out_messages[], int items_per_page)
{
    int start_index = page_index * items_per_page;
    int items_to_return = 0;

    for (int i = 0; i < items_per_page; i++) {
        int current_sms_index = start_index + i;
        if (current_sms_index >= MAX_SMS_COUNT) {
            break; // No more messages
        }

        snprintf(out_messages[i].phone_number, MAX_PHONE_NUMBER_LEN, "186%08d", current_sms_index);
        items_to_return++;
    }

    return items_to_return;
}