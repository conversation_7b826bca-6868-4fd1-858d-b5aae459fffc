#ifndef __UBUS_HELPER_H__
#define __UBUS_HELPER_H__

#include <cjson/cJSON.h>
#include <libubus.h>

/**
 * @brief Callback function type for dispatched ubus events.
 * @param event The name of the event from the ubus call payload.
 * @param data The cJSON data associated with the event.
 */
typedef void (*gui_ubus_event_cb_t)(const char *event, cJSON *data);

/**
 * @brief Initializes the ubus connection and registers the GUI's ubus object.
 *
 * @param event_handler The callback function to handle incoming events.
 * @return 0 on success, a negative value on error.
 */
int ubus_helper_init(gui_ubus_event_cb_t event_handler);

/**
 * @brief Deinitializes the ubus connection.
 */
void ubus_helper_deinit(void);

/**
 * @brief A more generic synchronous ubus call function.
 * 
 * @param path The ubus object path.
 * @param method The method to call.
 * @param params_json A JSON string with parameters for the method. Can be NULL.
 * @return A pointer to a cJSON object with the result, or NULL on error.
 *         The caller is responsible for freeing the returned cJSON object.
 */
cJSON* ubus_call_sync(const char *path, const char *method, const char *params_json);

/**
 * @brief Gets the ubus file descriptor for event loop integration.
 *
 * @return The file descriptor on success, or -1 on error.
 */
int ubus_helper_get_fd(void);

/**
 * @brief Handles pending ubus events.
 * This should be called when the file descriptor returned by ubus_helper_get_fd()
 * indicates that there is data to be read.
 */
void ubus_helper_handle_events(void);


#endif /* __UBUS_HELPER_H__ */ 