
# Copyright (C) 2006-2014 OpenWrt.org
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

include $(TOPDIR)/rules.mk

PKG_NAME:=trc-gui
PKG_VERSION:=1.0
PKG_RELEASE:=$(AUTORELEASE)

LOCAL_SRC:=$(TOPDIR)/package/trc-app/trc-gui/src

PKG_FIXUP:=autoreconf
PKG_INSTALL:=1
PKG_BUILD_PARALLEL:=$(nproc)

#CONFIGURE_ARGS += --with-common-includes=${STAGING_DIR}
#CMAKE_SOURCE_DIR:=$(LOCAL_SRC)

include $(INCLUDE_DIR)/package.mk

define Package/trc-gui
	CATEGORY:=Tricheer
	TITLE:=Source Code Tricheer gui
	DEPENDS:=+liblvgl +libtrcsdk +libtrcapi +libuci
endef

define Package/trc-gui/description
  trc-gui is embedded gui code
endef

MAKE_PATH=.

TARGET_CFLAGS += -g -fpic -pie \
                 -I$(STAGING_DIR)/usr/include

TARGET_LDFLAGS += -L$(STAGING_DIR)/usr/lib -ltrcsdk -luci

define Build/Prepare
	$(info Building Prepare Information...)
	$(INSTALL_DIR) $(PKG_BUILD_DIR)/
	$(CP) $(LOCAL_SRC)/* $(PKG_BUILD_DIR)/
	$(Build/Patch)
endef

define Package/trc-gui/install
	@echo "Installing trc-gui package ..."
	$(INSTALL_DIR) $(1)/usr/bin $(1)/etc/init.d $(1)/usr/share/trcgui
	$(INSTALL_BIN) $(PKG_INSTALL_DIR)/usr/bin/trc_gui $(1)/usr/bin
	$(CP) $(PKG_INSTALL_DIR)/etc/init.d/* $(1)/etc/init.d/
	$(CP) $(PKG_INSTALL_DIR)/usr/share/trcgui/* $(1)/usr/share/trcgui/
endef

$(eval $(call BuildPackage,trc-gui))

