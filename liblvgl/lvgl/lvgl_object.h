/***********************************************************************************************
 *
 *    Filename: lvgl_object.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:44:21 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __GUI_OBJECT_H__
#define __GUI_OBJECT_H__

#include <stdio.h>
#include <libubox/list.h>

#include "lvgl_api.h"

typedef void (*ObjectDrawFunc)(void *);

#define COLOR_GET_RED(rgb)              (((rgb) & 0xFF0000) >> 16)
#define COLOR_GET_GREEN(rgb)            (((rgb) & 0xFF00) >> 8)
#define COLOR_GET_BLUE(rgb)             (((rgb) & 0xFF) >> 0)

#define COLOR_BLACK                     0x000000
#define COLOR_RED                       0xFF0000
#define COLOR_GREEN                     0x00FF00
#define COLOR_BLUE                      0x0000FF
#define COLOR_WHITE                     0xFFFFFF

typedef enum text_align
{
    TA_LEFT = 0,
    TA_CENTER,
    TA_LINE_CENTER,
    TA_RIGHT
} e_text_align;

typedef struct _object_pos
{
    int x;
    int y;
    e_text_align align;
} object_pos;

typedef struct _object_size
{
    int w;
    int h;
} object_size;

typedef struct _object_rect
{
    int x;
    int y;
    int w;
    int h;
} object_rect;

typedef struct _object_color
{
    unsigned char r;
    unsigned char g;
    unsigned char b;
} object_color;

typedef struct _object_list
{
    struct list_head list;
    void *obj;
    ObjectDrawFunc draw_method;
} object_list;

typedef enum _e_screen_power
{
    eSCR_POWER_ON,
    eSCR_POWER_OFF,
} e_screen_power;

/*----------------------------------------------------------------------------
 * GUI Object
 *---------------------------------------------------------------------------*/
void *gui_init(void);
void gui_deinit(void *pt);
void gui_refresh_obj(void *pt);
void gui_get_screen_size(void *pt, object_size *psize);
void gui_add_obj(void *pt, void *obj, ObjectDrawFunc obj_draw_func);
void gui_remove_obj(void *pt, void *obj);
int gui_screen_power(int on);

/*----------------------------------------------------------------------------
 * Textbox Object
 *---------------------------------------------------------------------------*/
static inline object_color to_object_color(unsigned int rgb)
{
    object_color color;

    color.r = COLOR_GET_RED(rgb);
    color.g = COLOR_GET_GREEN(rgb);
    color.b = COLOR_GET_BLUE(rgb);

    return color;
}

void *textbox_init(void *dfb);
void textbox_deinit(void *pt);
void textbox_setup(void *pt, object_pos pos, unsigned int rgb, void *parent);
void textbox_set_text(void *pt, const char *string);
void textbox_draw(void *pt);

/*----------------------------------------------------------------------------
 * Image Object
 *---------------------------------------------------------------------------*/
typedef struct _img_array_item
{
    int key;
    char *path;
} img_array_item;

void *image_init(void *dfb);
void image_deinit(void *pt);
void image_setup(void *pt, object_rect rect, void *parent);
void image_set_array(void *pt, const img_array_item *array, int cnt);
void image_set_key(void *pt, int key);
void image_draw(void *pt);

/*----------------------------------------------------------------------------
 * Bar Object
 *---------------------------------------------------------------------------*/
void *bar_init(void *dfb);
void bar_deinit(void *pt);
void bar_setup(void *pt, object_rect rect, void *parent);
void bar_set_value(void *pt, int value);
void bar_draw(void *pt);

#endif /* __GUI_OBJECT_H__ */
