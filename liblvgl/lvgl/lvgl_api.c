/***********************************************************************************************
 *
 *    Filename: lvgl_api.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:44:53 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
 **********************************************************************************************/

#include <stdio.h>
#include <unistd.h>
#include <pthread.h>

#include "lvgl_api.h"


#define DISP_BUF_SIZE (LV_HOR_RES_MAX * LV_VER_RES_MAX)

static lv_disp_draw_buf_t g_disp_buf;
static lv_disp_drv_t g_disp_drv;
static lv_disp_t *g_disp = NULL;
static lv_fs_drv_t g_fs_drv;
/*A small buffer for LittlevGL to draw the screen's content*/
static lv_color_t g_buf[DISP_BUF_SIZE];

const uint8_t obj_bg_map[LV_HOR_RES_MAX * LV_VER_RES_MAX] = {0};
const lv_img_dsc_t obj_bg = {
    .header.cf = LV_IMG_CF_TRUE_COLOR,
    .header.always_zero = 0,
    .header.reserved = 0,
    .header.w = LV_HOR_RES_MAX,
    .header.h = LV_VER_RES_MAX,
    .data_size = LV_HOR_RES_MAX * LV_VER_RES_MAX * LV_COLOR_SIZE / 8,
    .data = obj_bg_map,
};

void *lvgl_create_obj(void *parent)
{
    lv_obj_t *obj = lv_img_create(NULL == parent ? lv_scr_act() : parent);
    lv_img_set_src(obj, &obj_bg);
    //lv_obj_t *obj = lv_obj_create(NULL == parent ? lv_scr_act() : parent);
    return (void *)obj;
}

int lvgl_del_obj(void *obj)
{
    if (obj)
    {
        lv_obj_del(obj);
    }

    return 0;
}

int lvgl_clean_obj(void *obj)
{
    if (obj)
    {
        lv_obj_clean(obj);
    }

    return 0;
}

void *lvgl_create_imgobj(void *parent)
{
    lv_obj_t *obj = lv_img_create(NULL == parent ? lv_scr_act() : parent);
    return obj;
}

void *lvgl_create_textobj(void *parent)
{
    lv_obj_t *obj = lv_label_create(NULL == parent ? lv_scr_act() : parent);
    return obj;
}

void *lvgl_create_barobj(void *parent)
{
    lv_obj_t *obj = lv_bar_create(NULL == parent ? lv_scr_act() : parent);
    return obj;
}

int lvgl_textbox_set(void *obj, const char *text)
{
    if (obj && text)
    {
        lv_label_set_text(obj, text);
    }

    return 0;
}

int lvgl_hide_obj(void *obj)
{
    if (obj) {
        lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN);
    }

    return 0;
}

int lvgl_show_obj(void *obj)
{
    if (obj) {
        lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN);
    }

    return 0;
}

int lvgl_set_pos(void *obj, int x, int y)
{
    if (obj)
    {
        lv_obj_set_pos(obj, x, y);
    }

    return 0;
}

int lvgl_set_size(void *obj, int w, int h)
{
    if (obj)
    {
        lv_obj_set_size(obj, w, h);
    }
    return 0;
}

static void *open_cb(struct _lv_fs_drv_t *drv, const char *path, lv_fs_mode_t mode)
{
    (void)drv;

    const char *flags = "";

    if (mode == LV_FS_MODE_WR)
        flags = "wb";
    else if (mode == LV_FS_MODE_RD)
        flags = "rb";
    else if (mode == (LV_FS_MODE_WR | LV_FS_MODE_RD))
        flags = "a+";

    return fopen(path, flags);
}

static lv_fs_res_t close_cb(struct _lv_fs_drv_t *drv, void *file_p)
{
    (void)drv;

    return fclose(file_p);
}

static lv_fs_res_t read_cb(struct _lv_fs_drv_t *drv, void *file_p, void *buf, uint32_t btr,
                           uint32_t *br)
{
    (void)drv;

    *br = (uint32_t)fread(buf, 1, btr, file_p);

    return (*br <= 0) ? LV_FS_RES_UNKNOWN : LV_FS_RES_OK;
}

static lv_fs_res_t write_cb(struct _lv_fs_drv_t *drv, void *file_p, const void *buf, uint32_t btw,
                              uint32_t *bw)
{
    (void)drv;

    *bw = (uint32_t)fwrite(buf, 1, btw, file_p);

    return LV_FS_RES_OK;
}

static lv_fs_res_t seek_cb(struct _lv_fs_drv_t *drv, void *file_p, uint32_t pos,
                           lv_fs_whence_t whence)
{
    (void)drv;

    return fseek(file_p, pos, whence);
}

static lv_fs_res_t tell_cb(struct _lv_fs_drv_t *drv, void *file_p, uint32_t *pos_p)
{
    (void)drv;

    *pos_p = ftell(file_p);

    return LV_FS_RES_OK;
}

static bool ready_cb(struct _lv_fs_drv_t *drv)
{
    (void)drv;
    return true;
}

int lvgl_deinit(void)
{
    lv_deinit();

    fbdev_exit();

    return 0;
}

int lvgl_init(void)
{
    /*LittlevGL init*/
    lv_init();

    /*Linux frame buffer device init*/
    fbdev_init();

    /*Initialize a descriptor for the buffer*/
    lv_disp_draw_buf_init(&g_disp_buf, g_buf, NULL, sizeof(g_buf));

    /*Initialize and register a display driver*/
    lv_disp_drv_init(&g_disp_drv);
    g_disp_drv.draw_buf = &g_disp_buf;
    g_disp_drv.flush_cb = fbdev_flush;
    g_disp_drv.hor_res = LV_HOR_RES_MAX;
    g_disp_drv.ver_res = LV_VER_RES_MAX;
    g_disp = lv_disp_drv_register(&g_disp_drv);

    lv_fs_drv_init(&g_fs_drv); /*Basic initialization*/

    g_fs_drv.letter = 'f';               /*An uppercase letter to identify the drive */
    g_fs_drv.ready_cb = ready_cb;        /*Callback to tell if the drive is ready to use */
    g_fs_drv.open_cb = open_cb;          /*Callback to open a file */
    g_fs_drv.close_cb = close_cb;        /*Callback to close a file */
    g_fs_drv.read_cb = read_cb;          /*Callback to read a file */
    g_fs_drv.seek_cb = seek_cb;          /*Callback to seek in a file (Move cursor) */
    g_fs_drv.tell_cb = tell_cb;          /*Callback to tell the cursor position  */
    lv_fs_drv_register(&g_fs_drv);       /*Finally register the drive*/

    return 0;
}

#define LCD_UPDATE "/sys/devices/platform/soc/9c0000.qcom,qupv3_0_geni_se/988000.spi/spi_master/spi0/spi0.0/lcd_update_flag"

int lvgl_update(void)
{
    lv_timer_handler();

    lv_file_write(LCD_UPDATE, "write");

    return 0;
}

int lvgl_get_res(int *w, int *h)
{
    if (w && h)
    {
        fbdev_get_sizes(w, h);
    }

    return 0;
}

int lvgl_set_power(int on)
{
    return fbdev_power(on);
}

int lvgl_set_img(void *obj, void *img)
{
    if (img && obj)
    {
        lv_img_set_src(obj, img);
    }

    return 0;
}
