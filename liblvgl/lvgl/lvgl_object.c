/***********************************************************************************************
 *
 *    Filename: lvgl_object.c
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:44:13 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
 **********************************************************************************************/

#include <stdio.h>

#include "lvgl_object.h"

typedef struct _gui_priv
{
    object_size screen;
    struct list_head obj_list_head;
} gui_priv;

void *gui_init(void)
{
    gui_priv *p = NULL;
    p = (gui_priv *)calloc(1, sizeof(*p));

    if (!p)
    {
        LV_LOG_ERROR("Out of memory!!!");
        return NULL;
    }

    INIT_LIST_HEAD(&p->obj_list_head);

    lvgl_get_res(&p->screen.w, &p->screen.h);

    return (void *)p;
}

void gui_deinit(void *pt)
{
    gui_priv *p = (gui_priv *)pt;

    if (p)
    {
        free(p);
        p = NULL;
    }
}

void gui_refresh_obj(void *pt)
{
    object_list *entry = NULL;
    gui_priv *p = (gui_priv *)pt;
    struct list_head *ptr;
    list_for_each(ptr, &p->obj_list_head)
    {
        entry = list_entry(ptr, object_list, list);
        LV_LOG_INFO("calling draw method of object %08X", entry->obj);
        if (entry->draw_method && entry->obj)
        {
            entry->draw_method(entry->obj);
        }
    }
    //lvgl_update();
}

void gui_get_screen_size(void *pt, object_size *psize)
{
    gui_priv *p = (gui_priv *)pt;

    if (p && psize)
    {
        psize->w = p->screen.w;
        psize->h = p->screen.h;
    }
}

void gui_add_obj(void *pt, void *obj, ObjectDrawFunc obj_draw_func)
{
    object_list *new_obj = NULL;
    gui_priv *p = (gui_priv *)pt;

    if (p)
    {
        new_obj = (object_list *)calloc(1, sizeof(*new_obj));
        if (NULL == new_obj)
        {
            LV_LOG_ERROR("calloc new object failed");
        }

        new_obj->obj = obj;
        new_obj->draw_method = obj_draw_func;
        list_add_tail(&new_obj->list, &p->obj_list_head);
    }
}

void gui_remove_obj(void *pt, void *obj)
{
    object_list *entry;
    struct list_head *ptr;
    gui_priv *p = (gui_priv *)pt;

    if (p)
    {
        list_for_each(ptr, &p->obj_list_head)
        {
            entry = list_entry(ptr, object_list, list);
            if (entry->obj == obj)
            {
                list_del(&entry->list);
                free(entry);
                return;
            }
        }
    }
}

int gui_screen_power(int on)
{
    lvgl_set_power(on);
    return on;
}

/*----------------------------------------------------------------------------
 * Textbox Object
 *---------------------------------------------------------------------------*/
typedef struct _textbox_priv
{
    void *dfb;

    void *obj;
    char *string;
    object_color color;
    object_pos pos;
    int size;
    int update;
} textbox_priv;

void *textbox_init(void *dfb)
{
    textbox_priv *textbox = (textbox_priv *)calloc(1, sizeof(*textbox));
    if (NULL == textbox)
    {
        LV_LOG_ERROR("calloc textbox failed");
        return NULL;
    }

    textbox->dfb = dfb;
    gui_add_obj(dfb, textbox, (ObjectDrawFunc)textbox_draw);

    return (void *)textbox;
}

void textbox_deinit(void *pt)
{
    textbox_priv *textbox = (textbox_priv *)pt;
    if (pt)
    {
        if (textbox->string)
        {
            free(textbox->string);
            textbox->string = NULL;
        }

        //lvgl_del_obj(textbox->obj);
        gui_remove_obj(textbox->dfb, textbox);
        free(textbox);
        textbox = NULL;
    }
}

void textbox_setup(void *pt, object_pos pos, unsigned int rgb, void *parent)
{
    textbox_priv *textbox = (textbox_priv *)pt;

    if (textbox)
    {
        lvgl_del_obj(textbox->obj);

        textbox->obj = lvgl_create_textobj(parent);
        textbox->pos.x = pos.x;
        textbox->pos.y = pos.y;
        textbox->pos.align = pos.align;
        textbox->color = to_object_color(rgb);
    }
}

void textbox_set_text(void *pt, const char *string)
{
    textbox_priv *textbox = (textbox_priv *)pt;

    if (NULL == textbox || NULL == string)
    {
        return;
    }

    if (NULL != textbox->string)
    {
        if (0 == strcmp(textbox->string, string))
        {
            return;
        }
        free(textbox->string);
    }

    textbox->string = strdup(string);
    textbox->update = 1;

    if (strlen(textbox->string) <= 0)
    {
        LV_LOG_INFO("textbox string is NULL hide obj");
        lvgl_hide_obj(textbox->obj);
        return;
    }

    lvgl_show_obj(textbox->obj);
    return;
}

void textbox_draw(void *pt)
{
    textbox_priv *textbox = (textbox_priv *)pt;

    if (NULL == textbox || NULL == textbox->string)
    {
        LV_LOG_ERROR("textbox_draw input parameter is NULL");
        return;
    }

    if (strlen(textbox->string) <= 0 || 0 == textbox->update)
    {
        LV_LOG_INFO("string is NULL or already update, do nothing");
        return;
    }

    textbox->update = 0;

    lvgl_textbox_set(textbox->obj, textbox->string);
    lvgl_set_pos(textbox->obj, textbox->pos.x, textbox->pos.y);

    lv_obj_set_width(textbox->obj, LV_HOR_RES_MAX);
    lv_label_set_long_mode(textbox->obj, LV_LABEL_LONG_SCROLL_CIRCULAR);

    if (textbox->pos.align == TA_CENTER)
    {
        lv_obj_set_style_text_align(textbox->obj, LV_TEXT_ALIGN_CENTER, 0);
        lv_obj_align(textbox->obj, LV_ALIGN_CENTER, 0, 0);
    }
    else if (textbox->pos.align == TA_LINE_CENTER)
    {
        lv_obj_set_style_text_align(textbox->obj, LV_TEXT_ALIGN_CENTER, 0);
    }
    else if (textbox->pos.align == TA_LEFT)
    {
        lv_obj_set_style_text_align(textbox->obj, LV_TEXT_ALIGN_LEFT, 0);
    }
    else if (textbox->pos.align == TA_RIGHT)
    {
        lv_obj_set_style_text_align(textbox->obj, LV_TEXT_ALIGN_RIGHT, 0);
    }
}

/*----------------------------------------------------------------------------
 * Image Object
 *---------------------------------------------------------------------------*/
typedef struct _image_array
{
    int items;
    int display_idx;
    img_array_item *array_items;
} image_array;

typedef struct _image_priv
{
    void *dfb;
    void *obj;
    image_array image_array;
} image_priv;

static void _image_free(image_priv *image)
{
    int i = 0;
    int cnt = image->image_array.items;

    for (i = 0; i < cnt; i++)
    {
        if (image->image_array.array_items[i].path != NULL)
        {
            free(image->image_array.array_items[i].path);
        }
    }

    free(image->image_array.array_items);
    image->image_array.array_items = NULL;
    image->image_array.items = 0;
    image->image_array.display_idx = -1;
}

void *image_init(void *dfb)
{
    image_priv *image = (image_priv *)calloc(1, sizeof(*image));
    if (NULL == image)
    {
        LV_LOG_ERROR("calloc image failed");
        return NULL;
    }

    image->dfb = dfb;
    gui_add_obj(image->dfb, image, (ObjectDrawFunc)image_draw);

    image->image_array.display_idx = -1;

    return (void *)image;
}

void image_deinit(void * pt)
{
    image_priv *image = (image_priv *)pt;

    if (image)
    {
        _image_free(image);
        //lvgl_del_obj(image->obj);
        gui_remove_obj(image->dfb, image);

        free(image);
        image = NULL;
    }
}

void image_setup(void *pt, object_rect rect, void *parent)
{
    image_priv *image = (image_priv *)pt;

    if (NULL == image)
    {
        LV_LOG_ERROR("image_setup input parameter is NULL");
        return;
    }

    lvgl_del_obj(image->obj);

    image->obj = lvgl_create_imgobj(parent);
    if (image->obj)
    {
        lvgl_set_pos(image->obj, rect.x, rect.y);
        lvgl_set_size(image->obj, rect.w, rect.h);
    }
}

void image_draw(void *pt)
{
    image_priv *image = (image_priv *)pt;
    img_array_item *img_item = NULL;

    if (NULL == image)
    {
        LV_LOG_ERROR("image_draw input parameter is NULL");
        return;
    }

    if (image->image_array.display_idx >= 0)
    {
        img_item = &image->image_array.array_items[image->image_array.display_idx];
        lvgl_set_img(image->obj, img_item->path);
    }
	return;
}

void image_set_array(void *pt, const img_array_item *array, int cnt)
{
    image_priv *image = (image_priv *)pt;
    int i;

    if (image)
    {
        if (NULL != image->image_array.array_items)
        {
            printf("Freeing image array\n");
            _image_free(image);
        }

        image->image_array.array_items = calloc(cnt, sizeof(img_array_item));

        for (i = 0; i < cnt; i++)
        {
            image->image_array.array_items[i].key = array[i].key;
            image->image_array.array_items[i].path = strdup(array[i].path);
        }
        image->image_array.items = cnt;
    }
}

void image_set_key(void *pt, int key)
{
    image_priv *image = (image_priv *)pt;
    int i = 0;

    if (image)
    {
        for (i = 0; i < image->image_array.items; i++)
        {
            if (key == image->image_array.array_items[i].key)
            {
                image->image_array.display_idx = i;
                lvgl_show_obj(image->obj);
                break;
            }
        }

        if (i >= image->image_array.items)
        {
            LV_LOG_ERROR("Out of Image array");
            lvgl_hide_obj(image->obj);
        }
    }
}

typedef struct _bar_priv
{
    void *dfb;
    void *obj;
    int value;
} bar_priv;

void *bar_init(void *dfb)
{
    bar_priv *barobj = (bar_priv *)calloc(1, sizeof(*barobj));
    if (NULL == barobj)
    {
        LV_LOG_ERROR("calloc bar failed");
        return NULL;
    }

    barobj->dfb = dfb;
    gui_add_obj(barobj->dfb, barobj, (ObjectDrawFunc)bar_draw);

    return (void *)barobj;
}

void bar_deinit(void *pt)
{
    bar_priv *barobj = (bar_priv *)pt;

    if (barobj)
    {
        // lvgl_del_obj(barobj->obj);
        gui_remove_obj(barobj->dfb, barobj);

        free(barobj);
        barobj = NULL;
    }
}

void bar_setup(void *pt, object_rect rect, void *parent)
{
    bar_priv *bar = (bar_priv *)pt;

    if (NULL == bar)
    {
        LV_LOG_ERROR("image_setup input parameter is NULL");
        return;
    }

    lvgl_del_obj(bar->obj);

    bar->obj = lvgl_create_barobj(parent);
    if (bar->obj)
    {
        lv_obj_set_style_border_color(bar->obj, lv_color_white(), LV_PART_MAIN);
        lv_obj_set_style_border_width(bar->obj, 1, LV_PART_MAIN);
        lv_obj_set_pos(bar->obj, rect.x, rect.y);
        lv_obj_set_size(bar->obj, rect.w, rect.h);
        lv_obj_set_style_radius(bar->obj, 0, LV_PART_MAIN);
        lv_obj_set_style_radius(bar->obj, 0, LV_PART_INDICATOR);
    }
}

void bar_set_value(void *pt, int value)
{
    bar_priv *bar = (textbox_priv *)pt;

    if (NULL == bar || value < 0)
    {
        return;
    }

    bar->value = value;

    return;
}

void bar_draw(void *pt)
{
    bar_priv *bar = (textbox_priv *)pt;

    if (NULL == bar)
    {
        LV_LOG_ERROR("bar_draw input parameter is NULL");
        return;
    }

    lv_bar_set_value(bar->obj, bar->value, LV_ANIM_OFF);

    return;
}