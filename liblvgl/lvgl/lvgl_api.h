/***********************************************************************************************
 *
 *    Filename: lvgl_api.h
 *
 * Description:
 *
 *     Version: 1.0
 *     Created: Thu 09 May 2024 09:45:03 AM UTC
 *    Revision: none
 *    Compiler: gcc
 *
 *      Author:
 *   Copyright: Copyright (c) 2022, XXXXXX Co.,Ltd
 *
 *   History:
 *   <Date>         | <Author>      | <Description>
 *---------------------------------------------------------------------
 *   xxxx/xx/xx     | xxxxx         |
**********************************************************************************************/

#ifndef __GUI_LVGL_H__
#define __GUI_LVGL_H__

#include <stdio.h>

#include "lv_drivers/display/fbdev.h"
#include "lvgl/lvgl.h"

void *lvgl_create_obj(void *parent);
int lvgl_del_obj(void *obj);
int lvgl_clean_obj(void *obj);

void *lvgl_create_imgobj(void *parent);
void *lvgl_create_textobj(void *parent);
void *lvgl_create_barobj(void *parent);

int lvgl_textbox_set(void *obj, const char *text);

int lvgl_hide_obj(void *obj);
int lvgl_show_obj(void *obj);
int lvgl_set_pos(void *obj, int x, int y);
int lvgl_set_size(void *obj, int w, int h);

int lvgl_init(void);
int lvgl_deinit(void);
int lvgl_update(void);
int lvgl_get_res(int *w, int *h);
int lvgl_set_power(int on);

int lvgl_set_img(void *obj, void *img);

#endif /* __GUI_LVGL_H__ */

