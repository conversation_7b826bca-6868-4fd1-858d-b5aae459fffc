
# Copyright (C) 2006-2014 OpenWrt.org
#
# This is free software, licensed under the GNU General Public License v2.
# See /LICENSE for more information.
#

include $(TOPDIR)/rules.mk

PKG_NAME:=liblvgl
PKG_VERSION:=8.4.0
PKG_RELEASE:=$(AUTORELEASE)

LOCAL_SRC:=$(TOPDIR)/package/trc-app/liblvgl/lvgl

PKG_INSTALL:=1
PKG_BUILD_PARALLEL:=1

include $(INCLUDE_DIR)/package.mk
include $(INCLUDE_DIR)/cmake.mk

TARGET_CFLAGS += $(FPIC) -fexceptions -rdynamic
CMAKE_SOURCE_DIR:=$(SVR_SOURCE_DIR)

define Build/Prepare
	mkdir -p $(PKG_BUILD_DIR)/
	$(CP) $(LOCAL_SRC)/* $(PKG_BUILD_DIR)/
	$(Build/Patch)
endef

MAKE_PATH=.

define Build/InstallDev
	$(INSTALL_DIR) $(1)/usr/lib $(1)/usr/include $(1)/usr/lib/pkgconfig/
	$(CP) $(PKG_INSTALL_DIR)/usr/include/lvgl/* $(1)/usr/include/
	$(CP) $(PKG_INSTALL_DIR)/usr/lib/pkgconfig/lvgl.pc $(1)/usr/lib/pkgconfig/
	$(CP) $(PKG_INSTALL_DIR)/usr/lib/liblvgl* $(1)/usr/lib
endef

define Package/liblvgl
  CATEGORY:=Tricheer
  TITLE:=lvgl library
  URL:=https://lvgl.io/
endef

define Package/liblvgl/description
  lvgl is an little VGL library.
endef

define Package/liblvgl/install
	@echo "Installing lvgl package ..."
endef

$(eval $(call BuildPackage,liblvgl))

